# Evercall TTS React 系统设计文档

## 概述

本设计文档基于需求文档，详细描述了使用React技术栈重新实现Evercall TTS系统的架构设计、组件结构、数据流和技术实现方案。

## 架构设计

### 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    前端 React 应用                           │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   UI 组件   │  │  状态管理   │  │  工具函数   │         │
│  │   层        │  │   (Zustand) │  │   层        │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ WebSocket   │  │  Live2D     │  │  粒子系统   │         │
│  │  服务       │  │   服务      │  │   服务      │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                              │
                    WebSocket 连接
                              │
┌─────────────────────────────────────────────────────────────┐
│                   后端 Node.js 服务                         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ WebSocket   │  │   TTS       │  │  音频处理   │         │
│  │  服务器     │  │   引擎      │  │   模块      │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

### 技术栈选择

#### 前端技术栈
- **React 18+**: 主框架，支持并发特性和Suspense
- **TypeScript**: 类型安全和开发体验
- **Zustand**: 轻量级状态管理
- **Tailwind CSS**: 原子化CSS框架
- **Radix UI**: 无样式、可访问的UI组件库
- **shadcn/ui**: 基于Radix UI的现代组件库
- **Lucide React**: 现代图标库
- **PIXI.js**: 2D图形渲染引擎
- **pixi-live2d-display**: Live2D模型渲染
- **React Query**: 服务端状态管理和缓存
- **Framer Motion**: 动画库
- **Vite**: 构建工具

#### 开发和构建工具
- **pnpm**: 包管理器
- **ESLint + Prettier**: 代码规范
- **Husky**: Git hooks
- **Vitest**: 单元测试
- **Storybook**: 组件开发和文档
- **Tailwind CSS IntelliSense**: VS Code扩展
- **PostCSS**: CSS处理器

## 项目目录结构

```
evercall-tts-react/
├── public/
│   ├── static/
│   │   └── live2d/                   # Live2D模型文件
│   │       ├── haru/                 # 官方免费模型1
│   │       │   ├── haru_greeter_t03.model3.json
│   │       │   ├── *.exp3.json       # 表情文件
│   │       │   └── *.motion3.json    # 动画文件
│   │       ├── mark/                 # 官方免费模型2
│   │       │   ├── mark_free_t03.model3.json
│   │       │   ├── *.exp3.json
│   │       │   └── *.motion3.json
│   │       └── natori/               # 官方免费模型3
│   │           ├── natori_free_t02.model3.json
│   │           ├── *.exp3.json
│   │           └── *.motion3.json
│   └── favicon.ico
├── src/
│   ├── components/                    # React组件
│   │   ├── ui/                       # shadcn/ui基础组件
│   │   │   ├── button.tsx
│   │   │   ├── card.tsx
│   │   │   ├── badge.tsx
│   │   │   ├── textarea.tsx
│   │   │   ├── progress.tsx
│   │   │   ├── scroll-area.tsx
│   │   │   ├── toast.tsx
│   │   │   └── use-toast.ts
│   │   ├── layout/                   # 布局组件
│   │   │   ├── AppLayout.tsx
│   │   │   ├── MainPanel.tsx
│   │   │   ├── InfoPanel.tsx
│   │   │   └── GridBackground.tsx
│   │   ├── features/                 # 功能组件
│   │   │   ├── tts/
│   │   │   │   ├── TextInputSection.tsx
│   │   │   │   ├── SynthesizeButton.tsx
│   │   │   │   ├── ProgressPanel.tsx
│   │   │   │   └── AudioPlayer.tsx
│   │   │   ├── live2d/
│   │   │   │   ├── Live2DPanel.tsx
│   │   │   │   ├── Live2DContainer.tsx
│   │   │   │   └── Live2DControls.tsx
│   │   │   ├── audio-cache/
│   │   │   │   ├── AudioCachePanel.tsx
│   │   │   │   ├── AudioCacheItem.tsx
│   │   │   │   └── AudioCacheList.tsx
│   │   │   ├── particles/
│   │   │   │   ├── ParticleSystem.tsx
│   │   │   │   └── ParticleCanvas.tsx
│   │   │   └── logs/
│   │   │       ├── LogPanel.tsx
│   │   │       └── LogEntry.tsx
│   │   ├── common/                   # 通用组件
│   │   │   ├── StatusIndicator.tsx
│   │   │   ├── ErrorBoundary.tsx
│   │   │   └── LoadingSpinner.tsx
│   │   └── providers/                # Context提供者
│   │       ├── ThemeProvider.tsx
│   │       └── QueryProvider.tsx
│   ├── services/                     # 服务层
│   │   ├── websocket/
│   │   │   ├── WebSocketService.ts
│   │   │   ├── MessageDispatcher.ts
│   │   │   └── types.ts
│   │   ├── live2d/
│   │   │   ├── Live2DService.ts
│   │   │   └── Live2DTypes.ts
│   │   ├── particles/
│   │   │   ├── ParticleSystemService.ts
│   │   │   └── Particle.ts
│   │   └── audio/
│   │       ├── AudioProcessor.ts
│   │       └── AudioCache.ts
│   ├── store/                        # 状态管理
│   │   ├── appStore.ts              # 主要Zustand store
│   │   ├── slices/                  # Store切片
│   │   │   ├── websocketSlice.ts
│   │   │   ├── ttsSlice.ts
│   │   │   ├── live2dSlice.ts
│   │   │   ├── audioCacheSlice.ts
│   │   │   ├── logsSlice.ts
│   │   │   └── uiSlice.ts
│   │   └── types.ts                 # Store类型定义
│   ├── hooks/                       # 自定义Hooks
│   │   ├── useWebSocket.ts
│   │   ├── useTTS.ts
│   │   ├── useLive2D.ts
│   │   ├── useAudioCache.ts
│   │   ├── useParticles.ts
│   │   ├── useKeyboardShortcuts.ts
│   │   └── useNotifications.ts
│   ├── utils/                       # 工具函数
│   │   ├── audioUtils.ts
│   │   ├── formatUtils.ts
│   │   ├── logger.ts
│   │   └── constants.ts
│   ├── lib/                         # 库配置
│   │   ├── utils.ts                 # cn函数等工具
│   │   └── validations.ts           # 表单验证
│   ├── types/                       # TypeScript类型定义
│   │   ├── api.ts
│   │   ├── audio.ts
│   │   ├── live2d.ts
│   │   └── global.ts
│   ├── styles/                      # 样式文件
│   │   └── globals.css              # Tailwind CSS + 自定义样式
│   ├── App.tsx                      # 根组件
│   ├── main.tsx                     # 应用入口
│   └── vite-env.d.ts               # Vite类型声明
├── __tests__/                       # 测试文件
│   ├── components/
│   ├── services/
│   ├── hooks/
│   └── utils/
├── .env.development                 # 开发环境变量
├── .env.production                  # 生产环境变量
├── .eslintrc.json                   # ESLint配置
├── .prettierrc                      # Prettier配置
├── tailwind.config.js               # Tailwind配置
├── postcss.config.js                # PostCSS配置
├── tsconfig.json                    # TypeScript配置
├── vite.config.ts                   # Vite配置
├── package.json                     # 项目依赖
├── pnpm-lock.yaml                   # pnpm锁文件
└── README.md                        # 项目文档
```

## 组件架构设计

### 组件层次结构

```
App
├── ThemeProvider
├── QueryProvider
├── ErrorBoundary
├── GridBackground
├── ParticleSystem
├── AppLayout
│   ├── MainPanel
│   │   ├── StatusIndicator
│   │   ├── TextInputSection
│   │   ├── SynthesizeButton
│   │   ├── ProgressPanel
│   │   └── AudioPlayer
│   └── InfoPanel
│       ├── Live2DPanel
│       ├── AudioCachePanel
│       └── LogPanel
├── Live2DContainer (fixed position)
└── Toaster
```

### 目录结构说明

#### `/src/components/`
- **`ui/`**: shadcn/ui基础组件，提供一致的设计系统
- **`layout/`**: 页面布局相关组件
- **`features/`**: 按功能模块组织的业务组件
  - `tts/`: 文本转语音相关组件
  - `live2d/`: Live2D虚拟角色相关组件
  - `audio-cache/`: 音频缓存管理组件
  - `particles/`: 粒子特效系统组件
  - `logs/`: 系统日志相关组件
- **`common/`**: 通用可复用组件
- **`providers/`**: React Context提供者组件

#### `/src/services/`
- **`websocket/`**: WebSocket通信服务
- **`live2d/`**: Live2D模型管理服务
- **`particles/`**: 粒子系统服务
- **`audio/`**: 音频处理和缓存服务

#### `/src/store/`
- **`appStore.ts`**: 主要的Zustand store
- **`slices/`**: 按功能拆分的store切片
- **`types.ts`**: Store相关的TypeScript类型

#### `/src/hooks/`
- 自定义React Hooks，封装复杂的状态逻辑和副作用

#### `/src/utils/`
- 纯函数工具库，不依赖React或其他框架

#### `/src/lib/`
- 第三方库的配置和封装

#### `/src/types/`
- 全局TypeScript类型定义

#### `/src/styles/`
- 全局样式文件，主要是Tailwind CSS配置

### 关键文件说明

#### `src/main.tsx`
```typescript
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App.tsx';
import './styles/globals.css';

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
);
```

#### `src/App.tsx`
```typescript
import { ThemeProvider } from '@/components/providers/ThemeProvider';
import { QueryProvider } from '@/components/providers/QueryProvider';
import { ErrorBoundary } from '@/components/common/ErrorBoundary';
import { AppLayout } from '@/components/layout/AppLayout';
import { GridBackground } from '@/components/layout/GridBackground';
import { ParticleSystem } from '@/components/features/particles/ParticleSystem';
import { Live2DContainer } from '@/components/features/live2d/Live2DContainer';
import { Toaster } from '@/components/ui/toaster';

function App() {
  return (
    <ErrorBoundary>
      <ThemeProvider defaultTheme="dark" storageKey="evercall-theme">
        <QueryProvider>
          <div className="relative min-h-screen bg-background text-foreground overflow-x-hidden">
            <GridBackground />
            <ParticleSystem />
            <AppLayout />
            <Live2DContainer />
            <Toaster />
          </div>
        </QueryProvider>
      </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;
```

#### `src/lib/utils.ts`
```typescript
import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

export function formatTimestamp(date: Date): string {
  return date.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
}
```

#### `src/utils/constants.ts`
```typescript
export const WEBSOCKET_CONFIG = {
  RECONNECT_ATTEMPTS: 5,
  RECONNECT_DELAY: 3000,
  HEARTBEAT_INTERVAL: 30000,
} as const;

export const TTS_CONFIG = {
  MAX_TEXT_LENGTH: 5000,
  CHUNK_SIZE: 1024,
} as const;

export const AUDIO_CACHE_CONFIG = {
  MAX_ITEMS: 10,
  MAX_MEMORY_MB: 50,
} as const;

export const LIVE2D_CONFIG = {
  // Live2D官方免费模型配置
  MODELS: {
    haru: {
      name: 'Haru',
      path: '/static/live2d/haru/haru_greeter_t03.model3.json',
      expressions: ['f01', 'f02', 'f03', 'f04', 'f05', 'f06', 'f07', 'f08'],
      motions: {
        idle: 'idle_01',
        tap_body: 'tap_01',
        tap_head: 'flick_head_01',
        shake: 'shake_01',
      },
    },
    mark: {
      name: 'Mark',
      path: '/static/live2d/mark/mark_free_t03.model3.json',
      expressions: ['f01', 'f02', 'f03', 'f04', 'f05', 'f06', 'f07'],
      motions: {
        idle: 'idle_01',
        tap_body: 'tap_01',
        tap_head: 'flick_head_01',
        shake: 'shake_01',
      },
    },
    natori: {
      name: 'Natori',
      path: '/static/live2d/natori/natori_free_t02.model3.json',
      expressions: ['f01', 'f02', 'f03', 'f04', 'f05', 'f06'],
      motions: {
        idle: 'idle_01',
        tap_body: 'tap_01',
        tap_head: 'flick_head_01',
        shake: 'shake_01',
      },
    },
  },
  DEFAULT_MODEL: 'haru',
  SCALE: 0.3,
  POSITION: { x: 0.5, y: 0.9 }, // 相对位置
} as const;

export const PARTICLE_CONFIG = {
  COUNT: 200,
  SIZE: 1.8,
  SPEED: 0.003,
  REPEL_RADIUS: 100,
  REPEL_STRENGTH: 7,
} as const;

// Live2D官方模型下载地址
export const LIVE2D_OFFICIAL_MODELS = {
  // 官方免费模型下载链接
  download_urls: {
    haru: 'https://www.live2d.com/download/sample-data/',
    mark: 'https://www.live2d.com/download/sample-data/',
    natori: 'https://www.live2d.com/download/sample-data/',
  },
  // 或者使用CDN链接（如果可用）
  cdn_urls: {
    haru: 'https://cdn.jsdelivr.net/gh/guansss/pixi-live2d-display/test/assets/haru/',
    mark: 'https://cdn.jsdelivr.net/gh/guansss/pixi-live2d-display/test/assets/mark/',
    natori: 'https://cdn.jsdelivr.net/gh/guansss/pixi-live2d-display/test/assets/natori/',
  }
} as const;
```

### 核心组件设计

#### 1. App 组件
```typescript
import { Toaster } from '@/components/ui/toaster';
import { ThemeProvider } from '@/components/theme-provider';

interface AppProps {}

const App: React.FC<AppProps> = () => {
  return (
    <ThemeProvider defaultTheme="dark" storageKey="evercall-theme">
      <div className="min-h-screen bg-background text-foreground">
        {/* 全局状态初始化 */}
        {/* WebSocket连接管理 */}
        {/* 主要内容 */}
        <Toaster />
      </div>
    </ThemeProvider>
  );
}
```

#### 2. MainPanel 组件
```typescript
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';

interface MainPanelProps {
  className?: string;
}

const MainPanel: React.FC<MainPanelProps> = ({ className }) => {
  return (
    <Card className={cn("border-primary/20 bg-background/95 backdrop-blur", className)}>
      <CardHeader>
        <h1 className="text-4xl font-bold text-primary font-mono tracking-wider">
          EVERCALL
        </h1>
        <p className="text-secondary text-lg">TTS Terminal System</p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* TTS功能的主要界面 */}
        {/* 包含输入、按钮、进度显示等 */}
      </CardContent>
    </Card>
  );
}
```

#### 3. Live2DPanel 组件
```typescript
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Play, RotateCcw, Smile, User, Zap } from 'lucide-react';
import { LIVE2D_CONFIG } from '@/utils/constants';

interface Live2DPanelProps {
  width: number;
  height: number;
  currentModel: string;
  isLoaded: boolean;
  status: string;
  onModelLoad: () => void;
  onModelSwitch: (modelKey: string) => void;
  onReset: () => void;
  onExpressionToggle: () => void;
  onRandomMotion: () => void;
  onError?: (error: Error) => void;
}

const Live2DPanel: React.FC<Live2DPanelProps> = ({
  width,
  height,
  currentModel,
  isLoaded,
  status,
  onModelLoad,
  onModelSwitch,
  onReset,
  onExpressionToggle,
  onRandomMotion,
  onError
}) => {
  const modelConfig = LIVE2D_CONFIG.MODELS[currentModel];

  return (
    <Card className="border-green-500/20 bg-background/95 backdrop-blur">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-green-400 font-mono text-sm">
            DIGITAL ASSISTANT
          </CardTitle>
          <div className="flex gap-1">
            {!isLoaded && (
              <Button 
                size="sm" 
                variant="outline" 
                className="h-6 px-2 text-xs"
                onClick={onModelLoad}
              >
                <Play className="w-3 h-3 mr-1" />
                LOAD
              </Button>
            )}
            <Button 
              size="sm" 
              variant="outline" 
              className="h-6 px-2 text-xs"
              onClick={onReset}
            >
              <RotateCcw className="w-3 h-3 mr-1" />
              RESET
            </Button>
            <Button 
              size="sm" 
              variant="outline" 
              className="h-6 px-2 text-xs"
              onClick={onExpressionToggle}
              disabled={!isLoaded}
            >
              <Smile className="w-3 h-3 mr-1" />
              EXPR
            </Button>
            <Button 
              size="sm" 
              variant="outline" 
              className="h-6 px-2 text-xs"
              onClick={onRandomMotion}
              disabled={!isLoaded}
            >
              <Zap className="w-3 h-3 mr-1" />
              MOTION
            </Button>
          </div>
        </div>
        
        {/* 模型选择器 */}
        <div className="flex items-center gap-2 mt-2">
          <User className="w-4 h-4 text-muted-foreground" />
          <Select value={currentModel} onValueChange={onModelSwitch}>
            <SelectTrigger className="h-7 text-xs font-mono">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(LIVE2D_CONFIG.MODELS).map(([key, model]) => (
                <SelectItem key={key} value={key} className="text-xs font-mono">
                  {model.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        <div 
          className="relative bg-gradient-to-br from-green-500/10 to-primary/5 rounded-lg overflow-hidden"
          style={{ width, height }}
        >
          {/* Live2D模型渲染区域 */}
          <div id="live2d-canvas" className="w-full h-full" />
          
          {/* 状态显示 */}
          <div className="absolute bottom-2 left-2 right-2">
            <Badge 
              variant="secondary" 
              className={cn(
                "w-full justify-center text-xs",
                isLoaded && "bg-green-500/20 text-green-400"
              )}
            >
              {isLoaded ? `${modelConfig.name} - ${status}` : status}
            </Badge>
          </div>
          
          {/* 模型信息 */}
          {isLoaded && (
            <div className="absolute top-2 left-2">
              <Badge variant="outline" className="text-xs">
                {modelConfig.expressions.length} expressions
              </Badge>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
```

#### 4. AudioCachePanel 组件
```typescript
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Play, Trash2, Volume2 } from 'lucide-react';

interface AudioCacheItem {
  id: string;
  text: string;
  url: string;
  size: number;
  timestamp: Date;
}

interface AudioCachePanelProps {
  items: AudioCacheItem[];
  currentPlayingId?: string;
  onPlay: (id: string) => void;
  onDelete: (id: string) => void;
}

const AudioCachePanel: React.FC<AudioCachePanelProps> = ({
  items,
  currentPlayingId,
  onPlay,
  onDelete
}) => {
  return (
    <Card className="border-orange-500/20 bg-background/95 backdrop-blur">
      <CardHeader className="pb-3">
        <CardTitle className="text-orange-400 font-mono text-sm">
          AUDIO CACHE
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <ScrollArea className="h-[300px] px-4">
          <div className="space-y-2">
            {items.map((item, index) => (
              <div
                key={item.id}
                className={cn(
                  "p-3 rounded-lg border transition-all cursor-pointer hover:border-primary/50",
                  currentPlayingId === item.id 
                    ? "border-orange-500 bg-orange-500/10" 
                    : "border-border bg-background/50"
                )}
                onClick={() => onPlay(item.id)}
              >
                <div className="flex items-start justify-between gap-2">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-muted-foreground truncate">
                      {item.text}
                    </p>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="outline" className="text-xs">
                        {index === 9 ? '0' : (index + 1).toString()}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {item.timestamp.toLocaleTimeString()} | {formatBytes(item.size)}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center gap-1">
                    {currentPlayingId === item.id && (
                      <Volume2 className="w-4 h-4 text-orange-400" />
                    )}
                    <Button
                      size="sm"
                      variant="destructive"
                      className="h-6 w-6 p-0"
                      onClick={(e) => {
                        e.stopPropagation();
                        onDelete(item.id);
                      }}
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
        <div className="p-4 border-t">
          <p className="text-xs text-muted-foreground text-center">
            Press 1-9, 0 for quick playback
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
```

#### 5. ParticleSystem 组件
```typescript
interface ParticleSystemProps {
  width: number;
  height: number;
  isProcessing?: boolean;
}

const ParticleSystem: React.FC<ParticleSystemProps> = ({
  width,
  height,
  isProcessing
}) => {
  // Canvas粒子渲染
  // 鼠标交互处理
  // 性能优化
}
```

## 状态管理设计

### Zustand Store 结构

```typescript
interface AppState {
  // WebSocket状态
  websocket: {
    connection: WebSocket | null;
    status: 'disconnected' | 'connecting' | 'connected' | 'error';
    lastError?: string;
  };
  
  // TTS状态
  tts: {
    isProcessing: boolean;
    progress: {
      status: string;
      chunkCount: number;
      totalSize: number;
    };
    currentText: string;
  };
  
  // Live2D状态
  live2d: {
    isLoaded: boolean;
    currentExpression: number;
    status: string;
    error?: string;
  };
  
  // 音频缓存
  audioCache: {
    items: AudioCacheItem[];
    currentPlayingId?: string;
    maxItems: number;
  };
  
  // 系统日志
  logs: {
    entries: LogEntry[];
    maxEntries: number;
  };
  
  // UI状态
  ui: {
    isMobile: boolean;
    theme: 'dark';
    isProcessingAnimation: boolean;
  };
}

interface AppActions {
  // WebSocket操作
  connectWebSocket: () => void;
  disconnectWebSocket: () => void;
  sendMessage: (message: any) => void;
  
  // TTS操作
  startSynthesis: (text: string) => void;
  updateProgress: (progress: Partial<TTSProgress>) => void;
  completeSynthesis: (audioData: string) => void;
  
  // Live2D操作
  loadLive2DModel: () => Promise<void>;
  resetLive2DModel: () => void;
  toggleExpression: () => void;
  
  // 音频缓存操作
  addToCache: (item: Omit<AudioCacheItem, 'id'>) => void;
  removeFromCache: (id: string) => void;
  playFromCache: (id: string) => void;
  
  // 日志操作
  addLog: (entry: Omit<LogEntry, 'id' | 'timestamp'>) => void;
  clearLogs: () => void;
}
```

### Store 实现示例

```typescript
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

export const useAppStore = create<AppState & AppActions>()(
  devtools(
    (set, get) => ({
      // 初始状态
      websocket: {
        connection: null,
        status: 'disconnected',
      },
      tts: {
        isProcessing: false,
        progress: {
          status: 'Standby',
          chunkCount: 0,
          totalSize: 0,
        },
        currentText: '',
      },
      // ... 其他初始状态
      
      // Actions实现
      connectWebSocket: () => {
        const wsUrl = import.meta.env.VITE_WEBSOCKET_URL;
        const ws = new WebSocket(wsUrl);
        
        ws.onopen = () => {
          set((state) => ({
            websocket: { ...state.websocket, status: 'connected', connection: ws }
          }));
          get().addLog({ level: 'success', message: 'WebSocket连接成功' });
        };
        
        ws.onmessage = (event) => {
          const data = JSON.parse(event.data);
          handleWebSocketMessage(data);
        };
        
        ws.onclose = () => {
          set((state) => ({
            websocket: { ...state.websocket, status: 'disconnected', connection: null }
          }));
          // 自动重连逻辑
          setTimeout(() => get().connectWebSocket(), 3000);
        };
      },
      
      startSynthesis: (text: string) => {
        const { websocket } = get();
        if (websocket.connection && websocket.status === 'connected') {
          set((state) => ({
            tts: { ...state.tts, isProcessing: true, currentText: text }
          }));
          
          websocket.connection.send(JSON.stringify({
            type: 'tts_request',
            text: text
          }));
        }
      },
      
      // ... 其他actions实现
    }),
    { name: 'evercall-store' }
  )
);
```

## 服务层设计

### WebSocket服务

```typescript
class WebSocketService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 3000;
  
  connect(url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(url);
        
        this.ws.onopen = () => {
          this.reconnectAttempts = 0;
          resolve();
        };
        
        this.ws.onmessage = this.handleMessage.bind(this);
        this.ws.onclose = this.handleClose.bind(this);
        this.ws.onerror = this.handleError.bind(this);
        
      } catch (error) {
        reject(error);
      }
    });
  }
  
  send(data: any): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data));
    }
  }
  
  private handleMessage(event: MessageEvent): void {
    try {
      const data = JSON.parse(event.data);
      // 分发消息到相应的处理器
      MessageDispatcher.dispatch(data);
    } catch (error) {
      console.error('WebSocket消息解析错误:', error);
    }
  }
  
  private handleClose(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      setTimeout(() => {
        this.reconnectAttempts++;
        this.connect(this.ws?.url || '');
      }, this.reconnectDelay);
    }
  }
  
  disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}
```

### Live2D服务

```typescript
import { LIVE2D_CONFIG } from '@/utils/constants';

interface Live2DModel {
  name: string;
  path: string;
  expressions: string[];
  motions: Record<string, string>;
}

class Live2DService {
  private app: PIXI.Application | null = null;
  private model: any = null;
  private currentModelKey: string = LIVE2D_CONFIG.DEFAULT_MODEL;
  private currentExpressionIndex = 0;
  private isLoaded = false;
  
  async initialize(container: HTMLElement, modelKey?: string): Promise<void> {
    try {
      // 检查PIXI和Live2D库
      if (typeof PIXI === 'undefined') {
        throw new Error('PIXI.js未加载');
      }
      
      // 动态加载pixi-live2d-display
      if (typeof PIXI.live2d === 'undefined') {
        await this.loadLive2DLibrary();
      }
      
      // 创建PIXI应用
      this.app = new PIXI.Application({
        width: container.offsetWidth,
        height: container.offsetHeight,
        backgroundAlpha: 0,
        antialias: true,
        resolution: window.devicePixelRatio || 1,
      });
      
      container.appendChild(this.app.view as HTMLCanvasElement);
      
      // 加载指定的Live2D模型
      if (modelKey) {
        this.currentModelKey = modelKey;
      }
      await this.loadModel();
      
    } catch (error) {
      console.error('Live2D初始化失败:', error);
      this.createFallbackDisplay(container);
      throw error;
    }
  }
  
  async switchModel(modelKey: string): Promise<void> {
    if (!LIVE2D_CONFIG.MODELS[modelKey]) {
      throw new Error(`未知的模型: ${modelKey}`);
    }
    
    // 清除当前模型
    if (this.model && this.app) {
      this.app.stage.removeChild(this.model);
      this.model.destroy();
    }
    
    this.currentModelKey = modelKey;
    this.currentExpressionIndex = 0;
    
    await this.loadModel();
  }
  
  getCurrentModel(): Live2DModel {
    return LIVE2D_CONFIG.MODELS[this.currentModelKey];
  }
  
  getAvailableModels(): Record<string, Live2DModel> {
    return LIVE2D_CONFIG.MODELS;
  }
  
  private async loadModel(): Promise<void> {
    if (!this.app) throw new Error('PIXI应用未初始化');
    
    const modelConfig = this.getCurrentModel();
    
    try {
      // 从官方CDN或本地加载模型
      this.model = await PIXI.live2d.Live2DModel.from(modelConfig.path);
      
      // 设置模型属性
      this.model.scale.set(LIVE2D_CONFIG.SCALE);
      this.model.position.set(
        this.app.screen.width * LIVE2D_CONFIG.POSITION.x,
        this.app.screen.height * LIVE2D_CONFIG.POSITION.y
      );
      this.model.anchor.set(0.5, 1);
      
      this.app.stage.addChild(this.model);
      
      // 播放待机动画
      await this.playMotion('idle');
      
      this.isLoaded = true;
      
    } catch (error) {
      console.error(`模型加载失败 (${modelConfig.name}):`, error);
      throw error;
    }
  }
  
  private async loadLive2DLibrary(): Promise<void> {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/pixi-live2d-display@0.5.0/dist/index.min.js';
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Live2D库加载失败'));
      document.head.appendChild(script);
    });
  }
  
  async playMotion(motionType: string): Promise<void> {
    if (!this.model || !this.isLoaded) return;
    
    const modelConfig = this.getCurrentModel();
    const motionName = modelConfig.motions[motionType];
    
    if (!motionName) {
      console.warn(`动画类型不存在: ${motionType}`);
      return;
    }
    
    try {
      if (this.model.internalModel.motionManager) {
        await this.model.motion(motionName);
      }
    } catch (error) {
      console.error(`动画播放失败 (${motionName}):`, error);
    }
  }
  
  toggleExpression(): string | null {
    if (!this.model || !this.isLoaded) return null;
    
    const modelConfig = this.getCurrentModel();
    const expressionName = modelConfig.expressions[this.currentExpressionIndex];
    
    try {
      if (this.model.internalModel.expressionManager) {
        this.model.expression(expressionName);
      }
      
      this.currentExpressionIndex = 
        (this.currentExpressionIndex + 1) % modelConfig.expressions.length;
      
      return expressionName;
        
    } catch (error) {
      console.error('表情切换失败:', error);
      return null;
    }
  }
  
  async playRandomMotion(): Promise<void> {
    const motionTypes = ['tap_body', 'tap_head', 'shake'];
    const randomType = motionTypes[Math.floor(Math.random() * motionTypes.length)];
    await this.playMotion(randomType);
    
    // 2秒后回到待机动画
    setTimeout(() => {
      this.playMotion('idle');
    }, 2000);
  }
  
  reset(): void {
    if (this.app) {
      this.app.destroy(true);
      this.app = null;
      this.model = null;
    }
  }
  
  private createFallbackDisplay(container: HTMLElement): void {
    container.innerHTML = `
      <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #00ff88; font-family: 'Orbitron', monospace; text-align: center; flex-direction: column;">
        <div style="font-size: 3rem; margin-bottom: 15px; animation: pulse 2s infinite;">🤖</div>
        <div style="font-size: 1.2rem; margin-bottom: 5px;">LANHEI</div>
        <div style="font-size: 0.9rem; opacity: 0.8;">Digital Assistant</div>
        <div style="font-size: 0.7rem; opacity: 0.6; margin-top: 10px;">[Fallback Mode]</div>
      </div>
    `;
  }
}
```

### 粒子系统服务

```typescript
class ParticleSystemService {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private particles: Particle[] = [];
  private animationId: number | null = null;
  private mouse = { x: -1000, y: -1000 };
  
  constructor(canvas: HTMLCanvasElement) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d')!;
    this.initialize();
  }
  
  private initialize(): void {
    this.resizeCanvas();
    this.createLetterE();
    this.bindEvents();
    this.animate();
  }
  
  private createLetterE(): void {
    const centerX = this.canvas.width - 280;
    const centerY = this.canvas.height - 150;
    const radius = 120;
    
    this.particles = [];
    const targetPositions = this.generateEShape(centerX, centerY, radius);
    
    targetPositions.forEach((pos, index) => {
      const particle = new Particle(
        Math.random() * this.canvas.width,
        Math.random() * this.canvas.height,
        pos.x,
        pos.y,
        index
      );
      this.particles.push(particle);
    });
  }
  
  private generateEShape(centerX: number, centerY: number, radius: number): Array<{x: number, y: number}> {
    const positions: Array<{x: number, y: number}> = [];
    const spacing = 9;
    const strokeWidth = 3;
    
    // 生成圆形E的形状点位
    const outerRadius = radius;
    const innerRadius = radius - strokeWidth * spacing;
    
    // 外圆弧（右侧开口）
    for (let angle = Math.PI * 0.25; angle <= Math.PI * 1.75; angle += 0.08) {
      for (let r = innerRadius; r <= outerRadius; r += spacing) {
        const x = centerX + Math.cos(angle) * r;
        const y = centerY + Math.sin(angle) * r;
        positions.push({ x, y });
      }
    }
    
    // 中间水平线
    const lineY = centerY;
    const lineStartX = centerX - outerRadius + spacing;
    const lineEndX = centerX + innerRadius - spacing;
    
    for (let x = lineStartX; x <= lineEndX; x += spacing) {
      for (let t = -1; t <= 1; t++) {
        positions.push({
          x: x,
          y: lineY + t * spacing * 0.6
        });
      }
    }
    
    return positions;
  }
  
  private animate(): void {
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    
    this.particles.forEach(particle => {
      particle.update(this.mouse);
      particle.draw(this.ctx);
    });
    
    this.animationId = requestAnimationFrame(() => this.animate());
  }
  
  updateMousePosition(x: number, y: number): void {
    this.mouse.x = x;
    this.mouse.y = y;
  }
  
  destroy(): void {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }
  }
}

class Particle {
  constructor(
    public x: number,
    public y: number,
    public targetX: number,
    public targetY: number,
    public index: number
  ) {
    this.vx = 0;
    this.vy = 0;
    this.size = 1.8;
    this.opacity = 0.08;
    this.maxOpacity = 0.95;
  }
  
  update(mouse: {x: number, y: number}): void {
    // 粒子物理更新逻辑
    const dx = this.targetX - this.x;
    const dy = this.targetY - this.y;
    const targetDistance = Math.sqrt(dx ** 2 + dy ** 2);
    
    const mouseDistance = Math.sqrt(
      (mouse.x - this.x) ** 2 + (mouse.y - this.y) ** 2
    );
    
    // 鼠标排斥效果
    const repelRadius = 100;
    let repelForceX = 0;
    let repelForceY = 0;
    
    if (mouseDistance < repelRadius && mouseDistance > 0) {
      const repelStrength = Math.pow((repelRadius - mouseDistance) / repelRadius, 2);
      const angle = Math.atan2(this.y - mouse.y, this.x - mouse.x);
      repelForceX = Math.cos(angle) * repelStrength * 7;
      repelForceY = Math.sin(angle) * repelStrength * 7;
    }
    
    // 向目标位置的吸引力
    let attractStrength = 0.003;
    if (targetDistance > 450) attractStrength = 0.008;
    else if (targetDistance > 250) attractStrength = 0.005;
    
    const attractForceX = dx * attractStrength;
    const attractForceY = dy * attractStrength;
    
    // 更新速度和位置
    this.vx += attractForceX + repelForceX;
    this.vy += attractForceY + repelForceY;
    
    this.vx *= 0.9;
    this.vy *= 0.9;
    
    const maxSpeed = 8;
    const currentSpeed = Math.sqrt(this.vx ** 2 + this.vy ** 2);
    if (currentSpeed > maxSpeed) {
      this.vx = (this.vx / currentSpeed) * maxSpeed;
      this.vy = (this.vy / currentSpeed) * maxSpeed;
    }
    
    this.x += this.vx;
    this.y += this.vy;
    
    // 更新透明度
    this.updateOpacity(targetDistance, mouseDistance, repelRadius);
  }
  
  private updateOpacity(targetDistance: number, mouseDistance: number, repelRadius: number): void {
    let targetOpacity;
    
    if (targetDistance < 10) {
      targetOpacity = this.maxOpacity;
    } else if (targetDistance < 30) {
      targetOpacity = 0.9 * (1 - targetDistance / 60);
    } else if (targetDistance < 100) {
      targetOpacity = 0.5;
    } else {
      targetOpacity = 0.3;
    }
    
    if (mouseDistance < repelRadius) {
      const fadeStrength = 1 - (mouseDistance / repelRadius);
      targetOpacity *= (1 - fadeStrength * 0.8);
    }
    
    const opacitySpeed = 0.012;
    if (this.opacity < targetOpacity) {
      this.opacity = Math.min(targetOpacity, this.opacity + opacitySpeed);
    } else {
      this.opacity = Math.max(targetOpacity, this.opacity - opacitySpeed);
    }
    
    this.opacity = Math.max(0.1, Math.min(1, this.opacity));
  }
  
  draw(ctx: CanvasRenderingContext2D): void {
    if (this.opacity <= 0.05) return;
    
    ctx.save();
    
    const alpha = Math.min(1, this.opacity);
    
    // 主体填充
    ctx.fillStyle = `rgba(255, 255, 255, ${alpha})`;
    ctx.beginPath();
    ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
    ctx.fill();
    
    // 边框
    ctx.strokeStyle = `rgba(255, 255, 255, ${Math.min(1, alpha + 0.2)})`;
    ctx.lineWidth = 0.4;
    ctx.beginPath();
    ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
    ctx.stroke();
    
    // 内部高亮
    if (alpha > 0.4) {
      ctx.fillStyle = `rgba(255, 255, 255, ${Math.min(1, alpha * 1.2)})`;
      ctx.beginPath();
      ctx.arc(this.x, this.y, this.size * 0.6, 0, Math.PI * 2);
      ctx.fill();
    }
    
    ctx.restore();
  }
}
```

## 样式设计

### Tailwind CSS 配置

```typescript
// tailwind.config.js
import { fontFamily } from "tailwindcss/defaultTheme";

/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ["class"],
  content: ["./src/**/*.{ts,tsx}"],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "#00a2ff",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "#ff6600",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "#ff4757",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      fontFamily: {
        sans: ["Rajdhani", ...fontFamily.sans],
        mono: ["Orbitron", ...fontFamily.mono],
      },
      keyframes: {
        "accordion-down": {
          from: { height: 0 },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: 0 },
        },
        glow: {
          "0%, 100%": { boxShadow: "0 0 20px rgba(0, 162, 255, 0.3)" },
          "50%": { boxShadow: "0 0 40px rgba(0, 162, 255, 0.6)" },
        },
        pulse: {
          "0%, 100%": { opacity: 1 },
          "50%": { opacity: 0.3 },
        },
        gridMove: {
          "0%": { transform: "translate(0, 0)" },
          "100%": { transform: "translate(50px, 50px)" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        glow: "glow 2s infinite",
        pulse: "pulse 2s infinite",
        "grid-move": "gridMove 20s linear infinite",
      },
      backdropBlur: {
        xs: "2px",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
};
```

### CSS 变量定义

```css
/* globals.css */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@400;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 4%; /* #0a0a0a */
    --foreground: 0 0% 100%; /* #ffffff */
    --card: 210 40% 8%; /* rgba(16, 16, 16, 0.95) */
    --card-foreground: 0 0% 100%;
    --popover: 210 40% 8%;
    --popover-foreground: 0 0% 100%;
    --primary: 204 100% 50%; /* #00a2ff */
    --primary-foreground: 0 0% 0%;
    --secondary: 24 100% 50%; /* #ff6600 */
    --secondary-foreground: 0 0% 100%;
    --muted: 210 40% 12%;
    --muted-foreground: 0 0% 60%; /* #999999 */
    --accent: 210 40% 16%;
    --accent-foreground: 0 0% 100%;
    --destructive: 348 83% 64%; /* #ff4757 */
    --destructive-foreground: 0 0% 100%;
    --border: 210 40% 20%; /* #333333 */
    --input: 210 40% 16%;
    --ring: 204 100% 50%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .grid-background {
    @apply fixed inset-0 pointer-events-none z-0;
    background-image: 
      linear-gradient(rgba(0, 162, 255, 0.05) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 162, 255, 0.05) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 20s linear infinite;
  }
  
  .cyber-border {
    @apply relative;
  }
  
  .cyber-border::before,
  .cyber-border::after {
    @apply content-[''] absolute w-8 h-8 border-2 border-secondary;
  }
  
  .cyber-border::before {
    @apply -top-1 -left-1 border-r-0 border-b-0;
  }
  
  .cyber-border::after {
    @apply -bottom-1 -right-1 border-l-0 border-t-0;
  }
  
  .processing-glow {
    @apply animate-glow;
  }
}

/* 自定义滚动条 */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: theme(colors.primary) theme(colors.muted);
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 8px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    @apply bg-muted;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply bg-primary rounded;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    @apply bg-secondary;
  }
}
```

### shadcn/ui 组件定制

```typescript
// components/ui/button.tsx
import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium font-mono uppercase tracking-wider transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",
  {
    variants: {
      variant: {
        default: "bg-gradient-to-r from-secondary to-secondary/80 text-white hover:from-secondary/80 hover:to-secondary/60 shadow-lg hover:shadow-secondary/25",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline: "border border-input hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "underline-offset-4 hover:underline text-primary",
        cyber: "bg-gradient-to-r from-primary to-primary/80 text-white hover:from-primary/80 hover:to-primary/60 shadow-lg hover:shadow-primary/25 relative overflow-hidden before:absolute before:top-0 before:-left-full before:w-full before:h-full before:bg-gradient-to-r before:from-transparent before:via-white/30 before:to-transparent before:transition-all before:duration-500 hover:before:left-full",
      },
      size: {
        default: "h-10 py-2 px-4",
        sm: "h-9 px-3 rounded-md",
        lg: "h-11 px-8 rounded-md",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = "Button";

export { Button, buttonVariants };
```

## 数据流设计

### WebSocket消息处理流程

```typescript
// services/MessageDispatcher.ts
class MessageDispatcher {
  private static handlers: Map<string, (data: any) => void> = new Map();
  
  static register(type: string, handler: (data: any) => void): void {
    this.handlers.set(type, handler);
  }
  
  static dispatch(data: any): void {
    const handler = this.handlers.get(data.type);
    if (handler) {
      handler(data);
    } else {
      console.warn(`未知消息类型: ${data.type}`);
    }
  }
}

// 注册消息处理器
MessageDispatcher.register('welcome', (data) => {
  useAppStore.getState().addLog({
    level: 'success',
    message: data.message
  });
});

MessageDispatcher.register('tts_start', (data) => {
  useAppStore.getState().updateProgress({
    status: 'Generating Audio...'
  });
});

MessageDispatcher.register('audio_chunk', (data) => {
  const { updateProgress, addLog } = useAppStore.getState();
  
  updateProgress({
    chunkCount: data.chunk_id,
    totalSize: data.chunk_size
  });
  
  addLog({
    level: 'success',
    message: `音频块 #${data.chunk_id}: ${formatBytes(data.chunk_size)}`
  });
});

MessageDispatcher.register('tts_complete', (data) => {
  const { completeSynthesis, addLog } = useAppStore.getState();
  
  completeSynthesis(data.total_audio);
  
  addLog({
    level: 'success',
    message: `语音合成完成，总大小: ${formatBytes(data.total_size)}`
  });
});
```

### 音频处理流程

```typescript
// utils/audioUtils.ts
export class AudioProcessor {
  static hexToUint8Array(hexString: string): Uint8Array {
    const bytes = new Uint8Array(hexString.length / 2);
    for (let i = 0; i < hexString.length; i += 2) {
      bytes[i / 2] = parseInt(hexString.substr(i, 2), 16);
    }
    return bytes;
  }
  
  static createAudioBlob(hexData: string): Blob {
    const audioData = this.hexToUint8Array(hexData);
    return new Blob([audioData], { type: 'audio/mp3' });
  }
  
  static createAudioUrl(blob: Blob): string {
    return URL.createObjectURL(blob);
  }
  
  static revokeAudioUrl(url: string): void {
    URL.revokeObjectURL(url);
  }
  
  static formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
```

## 性能优化策略

### 1. React优化
- 使用React.memo包装纯组件
- 使用useMemo和useCallback缓存计算结果
- 实现虚拟滚动（如果日志条目过多）
- 使用Suspense和lazy loading

### 2. 渲染优化
- Canvas渲染使用requestAnimationFrame
- 粒子系统使用对象池减少GC
- Live2D模型懒加载
- 图片和资源预加载

### 3. 内存管理
- 音频URL及时释放
- WebSocket连接正确关闭
- 定时器和事件监听器清理
- 组件卸载时清理资源

### 4. 网络优化
- WebSocket心跳机制
- 音频数据分块传输
- 错误重试机制
- 连接池管理

## 错误处理和监控

### 错误边界组件

```typescript
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends React.Component<
  React.PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  constructor(props: React.PropsWithChildren<{}>) {
    super(props);
    this.state = { hasError: false };
  }
  
  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }
  
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('React错误边界捕获错误:', error, errorInfo);
    
    // 发送错误报告
    this.reportError(error, errorInfo);
  }
  
  private reportError(error: Error, errorInfo: React.ErrorInfo) {
    // 错误上报逻辑
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
    };
    
    // 发送到监控服务
    console.error('错误报告:', errorReport);
  }
  
  render() {
    if (this.state.hasError) {
      return (
        <div style={{ 
          padding: '2rem', 
          textAlign: 'center', 
          color: '#ff4757' 
        }}>
          <h2>系统出现错误</h2>
          <p>请刷新页面重试，如果问题持续存在请联系技术支持。</p>
          <button onClick={() => window.location.reload()}>
            刷新页面
          </button>
        </div>
      );
    }
    
    return this.props.children;
  }
}
```

### 日志系统

```typescript
// utils/logger.ts
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

export class Logger {
  private static instance: Logger;
  private logLevel: LogLevel = LogLevel.INFO;
  
  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }
  
  debug(message: string, data?: any): void {
    this.log(LogLevel.DEBUG, message, data);
  }
  
  info(message: string, data?: any): void {
    this.log(LogLevel.INFO, message, data);
  }
  
  warn(message: string, data?: any): void {
    this.log(LogLevel.WARN, message, data);
  }
  
  error(message: string, error?: Error | any): void {
    this.log(LogLevel.ERROR, message, error);
  }
  
  private log(level: LogLevel, message: string, data?: any): void {
    if (level < this.logLevel) return;
    
    const timestamp = new Date().toISOString();
    const levelName = LogLevel[level];
    
    console.log(`[${timestamp}] [${levelName}] ${message}`, data || '');
    
    // 添加到应用日志
    useAppStore.getState().addLog({
      level: levelName.toLowerCase() as any,
      message,
      data: data ? JSON.stringify(data) : undefined,
    });
  }
}
```

## 测试策略

### 单元测试

```typescript
// __tests__/components/MainPanel.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { MainPanel } from '../components/MainPanel';
import { useAppStore } from '../store/appStore';

// Mock store
jest.mock('../store/appStore');

describe('MainPanel', () => {
  beforeEach(() => {
    (useAppStore as jest.Mock).mockReturnValue({
      tts: {
        isProcessing: false,
        currentText: '',
      },
      startSynthesis: jest.fn(),
    });
  });
  
  it('应该渲染文本输入框', () => {
    render(<MainPanel />);
    expect(screen.getByPlaceholderText('请输入要转换为语音的文本...')).toBeInTheDocument();
  });
  
  it('应该在点击按钮时启动合成', () => {
    const mockStartSynthesis = jest.fn();
    (useAppStore as jest.Mock).mockReturnValue({
      tts: { isProcessing: false, currentText: '' },
      startSynthesis: mockStartSynthesis,
    });
    
    render(<MainPanel />);
    
    const textInput = screen.getByPlaceholderText('请输入要转换为语音的文本...');
    const synthesizeButton = screen.getByText('Initialize Synthesis');
    
    fireEvent.change(textInput, { target: { value: '测试文本' } });
    fireEvent.click(synthesizeButton);
    
    expect(mockStartSynthesis).toHaveBeenCalledWith('测试文本');
  });
});
```

### 集成测试

```typescript
// __tests__/integration/tts-workflow.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { App } from '../App';

// Mock WebSocket
global.WebSocket = jest.fn().mockImplementation(() => ({
  send: jest.fn(),
  close: jest.fn(),
  addEventListener: jest.fn(),
}));

describe('TTS工作流程集成测试', () => {
  it('应该完成完整的TTS流程', async () => {
    render(<App />);
    
    // 等待WebSocket连接
    await waitFor(() => {
      expect(screen.getByText('System Online')).toBeInTheDocument();
    });
    
    // 输入文本
    const textInput = screen.getByPlaceholderText('请输入要转换为语音的文本...');
    fireEvent.change(textInput, { target: { value: '测试语音合成' } });
    
    // 点击合成按钮
    const synthesizeButton = screen.getByText('Initialize Synthesis');
    fireEvent.click(synthesizeButton);
    
    // 验证处理状态
    expect(screen.getByText('Processing Request')).toBeInTheDocument();
    
    // 模拟接收音频数据
    // ... 测试逻辑
  });
});
```

## 部署配置

### Vite配置

```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@services': resolve(__dirname, 'src/services'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@store': resolve(__dirname, 'src/store'),
      '@lib': resolve(__dirname, 'src/lib'),
    },
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
      },
      '/ws': {
        target: 'ws://localhost:8080',
        ws: true,
      },
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          pixi: ['pixi.js', 'pixi-live2d-display'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-toast', 'lucide-react'],
          state: ['zustand', '@tanstack/react-query'],
        },
      },
    },
  },
  optimizeDeps: {
    include: ['pixi.js', 'zustand', '@radix-ui/react-dialog'],
  },
  css: {
    postcss: {
      plugins: [
        require('tailwindcss'),
        require('autoprefixer'),
      ],
    },
  },
});
```

### 环境配置

```typescript
// .env.development
VITE_WEBSOCKET_URL=ws://localhost:8080/ws
VITE_API_BASE_URL=http://localhost:8080/api
VITE_LIVE2D_MODEL_PATH=/static/lanhei
VITE_LOG_LEVEL=debug

// .env.production
VITE_WEBSOCKET_URL=wss://api.evercall.com/ws
VITE_API_BASE_URL=https://api.evercall.com/api
VITE_LIVE2D_MODEL_PATH=/static/lanhei
VITE_LOG_LEVEL=error
```

### 现代化UI组件示例

#### 状态指示器组件
```typescript
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { Wifi, WifiOff, Loader2 } from 'lucide-react';

interface StatusIndicatorProps {
  status: 'disconnected' | 'connected' | 'processing';
  message: string;
}

const StatusIndicator: React.FC<StatusIndicatorProps> = ({ status, message }) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'connected':
        return {
          icon: Wifi,
          className: 'border-green-500 text-green-400 bg-green-500/10',
          iconClassName: 'text-green-400',
        };
      case 'processing':
        return {
          icon: Loader2,
          className: 'border-orange-500 text-orange-400 bg-orange-500/10',
          iconClassName: 'text-orange-400 animate-spin',
        };
      default:
        return {
          icon: WifiOff,
          className: 'border-red-500 text-red-400 bg-red-500/10 animate-pulse',
          iconClassName: 'text-red-400',
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  return (
    <Badge 
      variant="outline" 
      className={cn(
        "font-mono uppercase tracking-wider px-4 py-2 text-sm border-l-4",
        config.className
      )}
    >
      <Icon className={cn("w-4 h-4 mr-2", config.iconClassName)} />
      {message}
    </Badge>
  );
};
```

#### 文本输入组件
```typescript
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Terminal } from 'lucide-react';

interface TextInputSectionProps {
  value: string;
  onChange: (value: string) => void;
  maxLength?: number;
  disabled?: boolean;
}

const TextInputSection: React.FC<TextInputSectionProps> = ({
  value,
  onChange,
  maxLength = 5000,
  disabled = false,
}) => {
  const remaining = maxLength - value.length;

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <Label 
          htmlFor="text-input" 
          className="text-primary font-mono uppercase tracking-wider flex items-center gap-2"
        >
          <Terminal className="w-4 h-4 text-secondary" />
          Text Input Protocol
        </Label>
        <Badge 
          variant={remaining < 100 ? "destructive" : "secondary"}
          className="font-mono text-xs"
        >
          {remaining} chars remaining
        </Badge>
      </div>
      
      <Textarea
        id="text-input"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder="请输入要转换为语音的文本..."
        className={cn(
          "min-h-[120px] bg-background/80 border-2 font-sans resize-y",
          "focus:border-primary focus:ring-2 focus:ring-primary/20",
          "transition-all duration-300"
        )}
        maxLength={maxLength}
        disabled={disabled}
      />
    </div>
  );
};
```

#### 进度面板组件
```typescript
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Activity, Database, Zap } from 'lucide-react';

interface ProgressPanelProps {
  status: string;
  chunkCount: number;
  totalSize: number;
  isVisible: boolean;
}

const ProgressPanel: React.FC<ProgressPanelProps> = ({
  status,
  chunkCount,
  totalSize,
  isVisible,
}) => {
  if (!isVisible) return null;

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Card className="border-primary/20 bg-primary/5">
      <CardHeader className="pb-3">
        <CardTitle className="text-primary font-mono text-sm uppercase tracking-wider">
          Progress
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Activity className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">Status:</span>
          </div>
          <Badge variant="outline" className="font-mono">
            {status}
          </Badge>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Zap className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">Audio Chunks:</span>
          </div>
          <Badge variant="outline" className="font-mono text-primary">
            {chunkCount}
          </Badge>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Database className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">Data Size:</span>
          </div>
          <Badge variant="outline" className="font-mono text-primary">
            {formatBytes(totalSize)}
          </Badge>
        </div>
        
        {chunkCount > 0 && (
          <div className="space-y-2">
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Processing...</span>
              <span>{Math.min(100, (chunkCount / 10) * 100).toFixed(0)}%</span>
            </div>
            <Progress 
              value={Math.min(100, (chunkCount / 10) * 100)} 
              className="h-2"
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
};
```

#### Toast通知系统
```typescript
import { useToast } from '@/components/ui/use-toast';
import { CheckCircle, XCircle, AlertCircle, Info } from 'lucide-react';

export const useNotifications = () => {
  const { toast } = useToast();

  const showSuccess = (message: string, description?: string) => {
    toast({
      title: message,
      description,
      duration: 3000,
      className: "border-green-500 bg-green-500/10",
      action: <CheckCircle className="w-4 h-4 text-green-400" />,
    });
  };

  const showError = (message: string, description?: string) => {
    toast({
      title: message,
      description,
      duration: 5000,
      variant: "destructive",
      action: <XCircle className="w-4 h-4" />,
    });
  };

  const showWarning = (message: string, description?: string) => {
    toast({
      title: message,
      description,
      duration: 4000,
      className: "border-orange-500 bg-orange-500/10",
      action: <AlertCircle className="w-4 h-4 text-orange-400" />,
    });
  };

  const showInfo = (message: string, description?: string) => {
    toast({
      title: message,
      description,
      duration: 3000,
      className: "border-primary bg-primary/10",
      action: <Info className="w-4 h-4 text-primary" />,
    });
  };

  return { showSuccess, showError, showWarning, showInfo };
};
```

### 包管理配置

#### package.json 依赖
```json
{
  "name": "evercall-tts-react",
  "private": true,
  "version": "0.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "preview": "vite preview",
    "test": "vitest",
    "test:ui": "vitest --ui"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "typescript": "^5.0.2",
    "zustand": "^4.4.1",
    "@radix-ui/react-dialog": "^1.0.5",
    "@radix-ui/react-toast": "^1.1.5",
    "@radix-ui/react-progress": "^1.0.3",
    "@radix-ui/react-scroll-area": "^1.0.5",
    "@radix-ui/react-label": "^2.0.2",
    "@radix-ui/react-slot": "^1.0.2",
    "lucide-react": "^0.263.1",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.0.0",
    "tailwind-merge": "^1.14.0",
    "tailwindcss-animate": "^1.0.7",
    "pixi.js": "^7.2.4",
    "pixi-live2d-display": "^0.5.0",
    "@tanstack/react-query": "^4.32.6",
    "framer-motion": "^10.16.4"
  },
  "devDependencies": {
    "@types/react": "^18.2.15",
    "@types/react-dom": "^18.2.7",
    "@vitejs/plugin-react": "^4.0.3",
    "vite": "^4.4.5",
    "tailwindcss": "^3.3.0",
    "autoprefixer": "^10.4.14",
    "postcss": "^8.4.27",
    "eslint": "^8.45.0",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "eslint-plugin-react-hooks": "^4.6.0",
    "eslint-plugin-react-refresh": "^0.4.3",
    "prettier": "^3.0.0",
    "prettier-plugin-tailwindcss": "^0.5.3",
    "vitest": "^0.34.0",
    "@testing-library/react": "^13.4.0",
    "@testing-library/jest-dom": "^5.17.0",
    "husky": "^8.0.3",
    "lint-staged": "^13.2.3"
  }
}
```

## Live2D官方模型集成指南

### 模型获取方式

#### 方式1：官方下载
1. 访问 [Live2D官方网站](https://www.live2d.com/download/sample-data/)
2. 下载免费的示例模型（Haru、Mark、Natori等）
3. 解压到 `public/static/live2d/` 对应目录

#### 方式2：CDN引用
```typescript
// 直接使用CDN链接，无需下载
const LIVE2D_CDN_CONFIG = {
  MODELS: {
    haru: {
      name: 'Haru',
      path: 'https://cdn.jsdelivr.net/gh/guansss/pixi-live2d-display/test/assets/haru/haru_greeter_t03.model3.json',
      // ...其他配置
    },
    // ...其他模型
  }
};
```

#### 方式3：动态加载
```typescript
// 动态从多个源加载模型
class Live2DModelLoader {
  private static async loadFromSources(modelKey: string): Promise<string> {
    const sources = [
      `/static/live2d/${modelKey}/`, // 本地文件
      `https://cdn.jsdelivr.net/gh/guansss/pixi-live2d-display/test/assets/${modelKey}/`, // CDN
      `https://api.live2d.com/models/${modelKey}/`, // 官方API（假设）
    ];
    
    for (const source of sources) {
      try {
        const response = await fetch(`${source}model3.json`);
        if (response.ok) {
          return source;
        }
      } catch (error) {
        console.warn(`Failed to load from ${source}:`, error);
      }
    }
    
    throw new Error(`无法加载模型: ${modelKey}`);
  }
}
```

### 模型配置说明

#### 标准Live2D模型结构
```
model_name/
├── model_name.model3.json          # 主模型文件
├── model_name.moc3                 # 模型数据
├── textures/                       # 纹理文件夹
│   ├── texture_00.png
│   └── texture_01.png
├── motions/                        # 动画文件夹
│   ├── idle_01.motion3.json
│   ├── tap_01.motion3.json
│   └── ...
└── expressions/                    # 表情文件夹
    ├── f01.exp3.json
    ├── f02.exp3.json
    └── ...
```

#### 模型适配配置
```typescript
// 根据实际模型文件调整配置
export const adaptModelConfig = (modelName: string, modelData: any) => {
  return {
    name: modelData.FileReferences?.Moc || modelName,
    expressions: modelData.FileReferences?.Expressions?.map((exp: any) => 
      exp.Name || exp.File
    ) || [],
    motions: Object.keys(modelData.FileReferences?.Motions || {}).reduce((acc, key) => {
      acc[key] = modelData.FileReferences.Motions[key][0]?.File || key;
      return acc;
    }, {} as Record<string, string>),
  };
};
```

### 性能优化建议

#### 1. 模型预加载
```typescript
// 在应用启动时预加载常用模型
const preloadModels = async () => {
  const modelKeys = ['haru']; // 只预加载默认模型
  
  for (const key of modelKeys) {
    try {
      await PIXI.live2d.Live2DModel.from(LIVE2D_CONFIG.MODELS[key].path);
      console.log(`模型预加载成功: ${key}`);
    } catch (error) {
      console.warn(`模型预加载失败: ${key}`, error);
    }
  }
};
```

#### 2. 资源缓存
```typescript
// 实现模型资源缓存
class Live2DCache {
  private static cache = new Map<string, any>();
  
  static async getModel(key: string): Promise<any> {
    if (this.cache.has(key)) {
      return this.cache.get(key);
    }
    
    const model = await PIXI.live2d.Live2DModel.from(
      LIVE2D_CONFIG.MODELS[key].path
    );
    
    this.cache.set(key, model);
    return model;
  }
  
  static clearCache(): void {
    this.cache.clear();
  }
}
```

#### 3. 内存管理
```typescript
// 模型切换时的内存清理
const switchModelSafely = async (newModelKey: string) => {
  // 清理当前模型
  if (currentModel) {
    currentModel.destroy({ children: true, texture: true, baseTexture: true });
  }
  
  // 强制垃圾回收（如果支持）
  if (window.gc) {
    window.gc();
  }
  
  // 加载新模型
  currentModel = await Live2DCache.getModel(newModelKey);
};
```

### 许可证和使用条款

#### Live2D官方模型使用注意事项
1. **免费模型**: 仅供学习和非商业用途
2. **商业使用**: 需要购买Live2D商业许可证
3. **再分发**: 不得重新分发官方模型文件
4. **修改**: 可以修改模型参数，但不得修改原始资源文件

#### 推荐的许可证处理
```typescript
// 在应用中显示许可证信息
const Live2DLicense: React.FC = () => {
  return (
    <div className="text-xs text-muted-foreground p-2 border-t">
      <p>Live2D models are provided by Live2D Inc. for educational purposes.</p>
      <p>Commercial use requires proper licensing from Live2D Inc.</p>
      <a 
        href="https://www.live2d.com/en/terms/live2d-proprietary-software-license-agreement/"
        target="_blank"
        rel="noopener noreferrer"
        className="text-primary hover:underline"
      >
        View License Terms
      </a>
    </div>
  );
};
```

这个设计文档提供了使用现代React技术栈（包括Radix UI、Tailwind CSS、shadcn/ui、Lucide React）重新实现Evercall TTS系统的完整技术方案，特别集成了Live2D官方免费模型，包括组件架构、状态管理、服务层设计、现代化样式系统、性能优化、错误处理和测试策略等各个方面。