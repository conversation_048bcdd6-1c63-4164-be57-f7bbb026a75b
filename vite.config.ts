import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import { fileURLToPath } from 'url'

const __dirname = path.dirname(fileURLToPath(import.meta.url))

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  define: {
    // 确保 PIXI 全局可用
    global: 'globalThis',
  },
  optimizeDeps: {
    include: [
      'pixi.js',
      'pixi-live2d-display',
    ],
    exclude: [
      // 排除可能导致问题的依赖
    ]
  },
  server: {
    fs: {
      // 允许访问 public 目录中的文件
      allow: ['..']
    }
  }
})
