{"models": [{"id": "idol", "name": "Idol", "description": "可爱的偶像角色", "modelPath": "/static/live2d/models/idol/ldol.model3.json", "previewImage": "/static/live2d/models/idol/111.png", "expressions": [{"name": "表情1", "file": "1.exp3.json"}, {"name": "表情2", "file": "2.exp3.json"}, {"name": "表情3", "file": "3.exp3.json"}, {"name": "表情4", "file": "4.exp3.json"}, {"name": "表情5", "file": "5.exp3.json"}, {"name": "表情6", "file": "6.exp3.json"}, {"name": "表情7", "file": "7.exp3.json"}, {"name": "表情8", "file": "8.exp3.json"}], "motions": [{"name": "动作1", "file": "1.motion3.json"}]}, {"id": "<PERSON><PERSON><PERSON>", "name": "蓝黑", "description": "蓝黑色调的角色", "modelPath": "/static/live2d/models/lanhei/lanhei.model3.json", "previewImage": null, "expressions": [{"name": "棒棒糖", "file": "bangbangtang.exp3.json"}, {"name": "唱歌", "file": "changge.exp3.json"}, {"name": "打游戏", "file": "dayouxi.exp3.json"}, {"name": "黑脸", "file": "heilian.exp3.json"}, {"name": "黑衣", "file": "heiyi.exp3.json"}, {"name": "哭", "file": "ku.exp3.json"}, {"name": "脸红", "file": "lianhong.exp3.json"}, {"name": "圈圈", "file": "quanquan.exp3.json"}, {"name": "生气", "file": "shengqi.exp3.json"}, {"name": "手表", "file": "shoubiao.exp3.json"}, {"name": "星星", "file": "xingxing.exp3.json"}], "motions": [{"name": "场景1", "file": "Scene1.motion3.json"}]}, {"id": "haru", "name": "<PERSON><PERSON>", "description": "Live2D官方示例模型 - 春日少女", "modelPath": "/static/live2d/models/haru/haru_greeter_t03.model3.json", "previewImage": null, "expressions": [{"name": "默认", "file": "f01.exp3.json"}, {"name": "开心", "file": "f02.exp3.json"}, {"name": "生气", "file": "f03.exp3.json"}, {"name": "惊讶", "file": "f04.exp3.json"}, {"name": "困惑", "file": "f05.exp3.json"}, {"name": "害羞", "file": "f06.exp3.json"}, {"name": "眨眼", "file": "f07.exp3.json"}, {"name": "微笑", "file": "f08.exp3.json"}], "motions": [{"name": "待机", "file": "idle_01.motion3.json"}, {"name": "点击身体", "file": "tap_01.motion3.json"}, {"name": "轻拂头部", "file": "flick_head_01.motion3.json"}, {"name": "摇摆", "file": "shake_01.motion3.json"}]}, {"id": "mark", "name": "<PERSON>", "description": "Live2D官方示例模型 - 马克", "modelPath": "/static/live2d/models/mark/mark_free_t03.model3.json", "previewImage": null, "expressions": [{"name": "默认", "file": "f01.exp3.json"}, {"name": "开心", "file": "f02.exp3.json"}, {"name": "生气", "file": "f03.exp3.json"}, {"name": "惊讶", "file": "f04.exp3.json"}, {"name": "困惑", "file": "f05.exp3.json"}, {"name": "害羞", "file": "f06.exp3.json"}, {"name": "微笑", "file": "f07.exp3.json"}], "motions": [{"name": "待机", "file": "idle_01.motion3.json"}, {"name": "点击身体", "file": "tap_01.motion3.json"}, {"name": "轻拂头部", "file": "flick_head_01.motion3.json"}, {"name": "摇摆", "file": "shake_01.motion3.json"}]}, {"id": "natori", "name": "<PERSON><PERSON>", "description": "Live2D官方示例模型 - 名取", "modelPath": "/static/live2d/models/natori/natori_free_t02.model3.json", "previewImage": null, "expressions": [{"name": "默认", "file": "f01.exp3.json"}, {"name": "开心", "file": "f02.exp3.json"}, {"name": "生气", "file": "f03.exp3.json"}, {"name": "惊讶", "file": "f04.exp3.json"}, {"name": "困惑", "file": "f05.exp3.json"}, {"name": "微笑", "file": "f06.exp3.json"}], "motions": [{"name": "待机", "file": "idle_01.motion3.json"}, {"name": "点击身体", "file": "tap_01.motion3.json"}, {"name": "轻拂头部", "file": "flick_head_01.motion3.json"}, {"name": "摇摆", "file": "shake_01.motion3.json"}]}], "defaultModel": "idol", "version": "2.0.0", "cdnConfig": {"baseUrl": "https://cdn.jsdelivr.net/gh/guansss/pixi-live2d-display/test/assets", "fallbackUrls": ["https://unpkg.com/pixi-live2d-display@0.5.0/test/assets", "https://cdn.staticaly.com/gh/guansss/pixi-live2d-display/master/test/assets"], "timeout": 10000}, "officialModels": {"haru": {"downloadUrl": "https://www.live2d.com/download/sample-data/", "cdnUrl": "https://cdn.jsdelivr.net/gh/guansss/pixi-live2d-display/test/assets/haru/", "license": "Live2D Official Sample - For learning purposes only"}, "mark": {"downloadUrl": "https://www.live2d.com/download/sample-data/", "cdnUrl": "https://cdn.jsdelivr.net/gh/guansss/pixi-live2d-display/test/assets/mark/", "license": "Live2D Official Sample - For learning purposes only"}, "natori": {"downloadUrl": "https://www.live2d.com/download/sample-data/", "cdnUrl": "https://cdn.jsdelivr.net/gh/guansss/pixi-live2d-display/test/assets/natori/", "license": "Live2D Official Sample - For learning purposes only"}}}