{"Version": 3, "Meta": {"PhysicsSettingCount": 43, "TotalInputCount": 130, "TotalOutputCount": 178, "VertexCount": 212, "Fps": 120, "EffectiveForces": {"Gravity": {"X": 0, "Y": -1}, "Wind": {"X": 0, "Y": 0}}, "PhysicsDictionary": [{"Id": "PhysicsSetting1", "Name": "瞳孔"}, {"Id": "PhysicsSetting2", "Name": "z"}, {"Id": "PhysicsSetting3", "Name": "x"}, {"Id": "PhysicsSetting4", "Name": "y"}, {"Id": "PhysicsSetting5", "Name": "高光"}, {"Id": "PhysicsSetting6", "Name": "高光(2)"}, {"Id": "PhysicsSetting7", "Name": "睫毛"}, {"Id": "PhysicsSetting8", "Name": "耳朵"}, {"Id": "PhysicsSetting9", "Name": "耳朵(3)"}, {"Id": "PhysicsSetting10", "Name": "裙子"}, {"Id": "PhysicsSetting11", "Name": "裙子(2)"}, {"Id": "PhysicsSetting12", "Name": "裙子(3)"}, {"Id": "PhysicsSetting13", "Name": "飘带头"}, {"Id": "PhysicsSetting14", "Name": "飘带头(2)"}, {"Id": "PhysicsSetting15", "Name": "衣服"}, {"Id": "PhysicsSetting16", "Name": "手"}, {"Id": "PhysicsSetting17", "Name": "手(5)"}, {"Id": "PhysicsSetting18", "Name": "手(4)"}, {"Id": "PhysicsSetting19", "Name": "胸"}, {"Id": "PhysicsSetting20", "Name": "腰带"}, {"Id": "PhysicsSetting21", "Name": "腰带(2)"}, {"Id": "PhysicsSetting22", "Name": "飘带"}, {"Id": "PhysicsSetting23", "Name": "刘海"}, {"Id": "PhysicsSetting24", "Name": "刘海(2)"}, {"Id": "PhysicsSetting25", "Name": "刘海(3)"}, {"Id": "PhysicsSetting26", "Name": "刘海(4)"}, {"Id": "PhysicsSetting27", "Name": "刘海(5)"}, {"Id": "PhysicsSetting28", "Name": "cefa"}, {"Id": "PhysicsSetting29", "Name": "cefa(2)"}, {"Id": "PhysicsSetting30", "Name": "cefa(3)"}, {"Id": "PhysicsSetting31", "Name": "cefa(4)"}, {"Id": "PhysicsSetting32", "Name": "cefa(5)"}, {"Id": "PhysicsSetting33", "Name": "cefa(6)"}, {"Id": "PhysicsSetting34", "Name": "cefa(7)"}, {"Id": "PhysicsSetting35", "Name": "cefa(8)"}, {"Id": "PhysicsSetting36", "Name": "cefa(9)"}, {"Id": "PhysicsSetting37", "Name": "cefa(10)"}, {"Id": "PhysicsSetting38", "Name": "1"}, {"Id": "PhysicsSetting39", "Name": "后发"}, {"Id": "PhysicsSetting40", "Name": "后发(2)"}, {"Id": "PhysicsSetting41", "Name": "后发(3)"}, {"Id": "PhysicsSetting42", "Name": "蝴蝶结"}, {"Id": "PhysicsSetting43", "Name": "歪嘴"}]}, "PhysicsSettings": [{"Id": "PhysicsSetting1", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param"}, "VertexIndex": 1, "Scale": 0.9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param2"}, "VertexIndex": 2, "Scale": 0.9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.94, "Delay": 0.9, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.94, "Delay": 0.9, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.94, "Delay": 0.9, "Acceleration": 0.8, "Radius": 9}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting2", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.94, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.5, "Delay": 1.2, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.5, "Delay": 1.5, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -15, "Default": 0, "Maximum": 15}}}, {"Id": "PhysicsSetting3", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.94, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.5, "Delay": 1.3, "Acceleration": 2, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -25, "Default": 0, "Maximum": 25.1}}}, {"Id": "PhysicsSetting4", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.94, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.5, "Delay": 1.3, "Acceleration": 2, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -25, "Default": 0, "Maximum": 25}}}, {"Id": "PhysicsSetting5", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param4"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param5"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param6"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.93, "Delay": 0.9, "Acceleration": 0.9, "Radius": 9}, {"Position": {"X": 0, "Y": 9}, "Mobility": 0.93, "Delay": 0.9, "Acceleration": 0.9, "Radius": 9}, {"Position": {"X": 0, "Y": 18}, "Mobility": 0.93, "Delay": 0.9, "Acceleration": 0.9, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.93, "Delay": 0.9, "Acceleration": 0.9, "Radius": 9}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting6", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param40"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param41"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param42"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.94, "Delay": 0.95, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.94, "Delay": 0.95, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 16}, "Mobility": 0.94, "Delay": 0.95, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.94, "Delay": 0.95, "Acceleration": 0.8, "Radius": 8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting7", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param50"}, "Weight": 17, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "Param49"}, "Weight": 19, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamEyeBallX"}, "Weight": 15, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeBallY"}, "Weight": 26, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param7"}, "VertexIndex": 1, "Scale": 0.9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param8"}, "VertexIndex": 2, "Scale": 0.9, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.98, "Delay": 0.8, "Acceleration": 0.85, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.96, "Delay": 0.8, "Acceleration": 0.85, "Radius": 10}, {"Position": {"X": 0, "Y": 19.1}, "Mobility": 0.97, "Delay": 0.8, "Acceleration": 0.85, "Radius": 9.1}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting8", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 20, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh10"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh10"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh10"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.85, "Radius": 8}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.85, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.85, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.8, "Radius": 8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting9", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 20, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 20, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 20, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh11"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh11"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh11"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.85, "Radius": 8}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.85, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.85, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.8, "Radius": 8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting10", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 52, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 48, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 49, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 48, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param20"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param21"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.8, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.8, "Radius": 9}], "Normalization": {"Position": {"Minimum": -12, "Default": 0, "Maximum": 12}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting11", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 52, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 48, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 49, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 48, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param22"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param44"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting12", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 52, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 48, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param24"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param25"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param26"}, "VertexIndex": 3, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.85, "Radius": 10}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.85, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.85, "Radius": 10}, {"Position": {"X": 0, "Y": 29}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.85, "Radius": 9}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting13", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 54, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 46, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh48"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh48"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh48"}, "VertexIndex": 3, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.9, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting14", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 54, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 46, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh53"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh53"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh53"}, "VertexIndex": 3, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.9, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting15", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 59, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 41, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param23"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param33"}, "VertexIndex": 3, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param34"}, "VertexIndex": 1, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 0.8, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 0.8, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting16", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 70, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation19"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation20"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation21"}, "VertexIndex": 3, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation22"}, "VertexIndex": 4, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.89, "Acceleration": 0.8, "Radius": 15}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.92, "Delay": 0.89, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 19.8}, "Mobility": 0.92, "Delay": 0.89, "Acceleration": 0.8, "Radius": 9.8}, {"Position": {"X": 0, "Y": 29.4}, "Mobility": 0.92, "Delay": 0.89, "Acceleration": 0.8, "Radius": 9.6}, {"Position": {"X": 0, "Y": 38.6}, "Mobility": 0.92, "Delay": 0.89, "Acceleration": 0.8, "Radius": 9.2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting17", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 70, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param16"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation28"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation29"}, "VertexIndex": 3, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param43"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 29}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 38}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.8, "Radius": 9}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting18", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 70, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation23"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation24"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation25"}, "VertexIndex": 3, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation26"}, "VertexIndex": 4, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.89, "Acceleration": 0.8, "Radius": 15}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.92, "Delay": 0.89, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 19.8}, "Mobility": 0.92, "Delay": 0.89, "Acceleration": 0.8, "Radius": 9.8}, {"Position": {"X": 0, "Y": 29.4}, "Mobility": 0.92, "Delay": 0.89, "Acceleration": 0.8, "Radius": 9.6}, {"Position": {"X": 0, "Y": 38.6}, "Mobility": 0.92, "Delay": 0.89, "Acceleration": 0.8, "Radius": 9.2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting19", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param17"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param18"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param19"}, "VertexIndex": 3, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.85, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.85, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.85, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.85, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting20", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 54, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 46, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 74, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 26, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param29"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param38"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param39"}, "VertexIndex": 3, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.93, "Delay": 0.85, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 0.85, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.93, "Delay": 0.85, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.93, "Delay": 0.85, "Acceleration": 0.8, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting21", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 54, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 46, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 73, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 27, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param30"}, "VertexIndex": 1, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param31"}, "VertexIndex": 2, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param32"}, "VertexIndex": 3, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.91, "Delay": 0.9, "Acceleration": 0.9, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.91, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.9, "Acceleration": 0.9, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.9, "Acceleration": 0.9, "Radius": 8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting22", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 57, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 43, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param27"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.98, "Delay": 0.85, "Acceleration": 0.85, "Radius": 12}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.98, "Delay": 0.85, "Acceleration": 0.85, "Radius": 12}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting23", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh110"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh110"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh110"}, "VertexIndex": 3, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh110"}, "VertexIndex": 4, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh110"}, "VertexIndex": 5, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 29}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 9}, {"Position": {"X": 0, "Y": 37}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 44}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 7}, {"Position": {"X": 0, "Y": 51}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 7}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting24", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh116"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh116"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh116"}, "VertexIndex": 3, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh116"}, "VertexIndex": 4, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh116"}, "VertexIndex": 5, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 29}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 9}, {"Position": {"X": 0, "Y": 37}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 44}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 7}, {"Position": {"X": 0, "Y": 51}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 7}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting25", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh109"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh109"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh109"}, "VertexIndex": 3, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh109"}, "VertexIndex": 4, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh109"}, "VertexIndex": 5, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 29}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 9}, {"Position": {"X": 0, "Y": 37}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 44}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 7}, {"Position": {"X": 0, "Y": 51}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 7}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting26", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh121"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh121"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh121"}, "VertexIndex": 3, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 29}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 9}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting27", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh122"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh122"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh122"}, "VertexIndex": 3, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 29}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 9}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting28", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh115"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh115"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh115"}, "VertexIndex": 3, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh115"}, "VertexIndex": 4, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh115"}, "VertexIndex": 5, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh115"}, "VertexIndex": 6, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh115"}, "VertexIndex": 6, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 6}, {"Position": {"X": 0, "Y": 46}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting29", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh117"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh117"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh117"}, "VertexIndex": 3, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh117"}, "VertexIndex": 4, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh117"}, "VertexIndex": 5, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh117"}, "VertexIndex": 6, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh117"}, "VertexIndex": 6, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 6}, {"Position": {"X": 0, "Y": 46}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting30", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh113"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh113"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh113"}, "VertexIndex": 3, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh113"}, "VertexIndex": 4, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh113"}, "VertexIndex": 5, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh113"}, "VertexIndex": 6, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh113"}, "VertexIndex": 6, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 6}, {"Position": {"X": 0, "Y": 46}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting31", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh107"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh107"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh107"}, "VertexIndex": 3, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh107"}, "VertexIndex": 4, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh107"}, "VertexIndex": 5, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh107"}, "VertexIndex": 6, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh107"}, "VertexIndex": 6, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 6}, {"Position": {"X": 0, "Y": 46}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting32", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh111"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh111"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh111"}, "VertexIndex": 3, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh111"}, "VertexIndex": 4, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh111"}, "VertexIndex": 5, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh111"}, "VertexIndex": 6, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh111"}, "VertexIndex": 6, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 6}, {"Position": {"X": 0, "Y": 46}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting33", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh119"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh119"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh119"}, "VertexIndex": 3, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh119"}, "VertexIndex": 4, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh119"}, "VertexIndex": 5, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh119"}, "VertexIndex": 6, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh119"}, "VertexIndex": 6, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 6}, {"Position": {"X": 0, "Y": 46}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting34", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh120"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh120"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh120"}, "VertexIndex": 3, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh120"}, "VertexIndex": 4, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh120"}, "VertexIndex": 5, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh120"}, "VertexIndex": 6, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh120"}, "VertexIndex": 6, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 6}, {"Position": {"X": 0, "Y": 46}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting35", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh112"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh112"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh112"}, "VertexIndex": 3, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh112"}, "VertexIndex": 4, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh112"}, "VertexIndex": 5, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh112"}, "VertexIndex": 6, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh112"}, "VertexIndex": 6, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 6}, {"Position": {"X": 0, "Y": 46}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting36", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh118"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh118"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh118"}, "VertexIndex": 3, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh118"}, "VertexIndex": 4, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh118"}, "VertexIndex": 5, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh118"}, "VertexIndex": 6, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh118"}, "VertexIndex": 6, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 6}, {"Position": {"X": 0, "Y": 46}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting37", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh123"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh123"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh123"}, "VertexIndex": 3, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh123"}, "VertexIndex": 4, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh123"}, "VertexIndex": 5, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh123"}, "VertexIndex": 6, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh123"}, "VertexIndex": 6, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 6}, {"Position": {"X": 0, "Y": 46}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.8, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting38", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 70, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairBack"}, "VertexIndex": 1, "Scale": 0.6, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBack2"}, "VertexIndex": 2, "Scale": 0.6, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBack3"}, "VertexIndex": 3, "Scale": 0.6, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 16}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 0.8, "Radius": 8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting39", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 48, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh190"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh190"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh190"}, "VertexIndex": 3, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh190"}, "VertexIndex": 4, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh190"}, "VertexIndex": 5, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh190"}, "VertexIndex": 6, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh190"}, "VertexIndex": 6, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 9}, "Mobility": 0.92, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 18}, "Mobility": 0.92, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.92, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.92, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.92, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting40", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 48, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh191"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh191"}, "VertexIndex": 2, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh191"}, "VertexIndex": 3, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh191"}, "VertexIndex": 4, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh191"}, "VertexIndex": 5, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh191"}, "VertexIndex": 6, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh191"}, "VertexIndex": 6, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 9}, "Mobility": 0.92, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 18}, "Mobility": 0.92, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.92, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.92, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.92, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting41", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 48, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh192"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh192"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh192"}, "VertexIndex": 3, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh192"}, "VertexIndex": 4, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh192"}, "VertexIndex": 5, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh192"}, "VertexIndex": 6, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh192"}, "VertexIndex": 6, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 9}, "Mobility": 0.92, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 18}, "Mobility": 0.92, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.92, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.92, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.92, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 0.8, "Radius": 9}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting42", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 48, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh52"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh52"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh52"}, "VertexIndex": 3, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh52"}, "VertexIndex": 4, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh52"}, "VertexIndex": 5, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.8, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting43", "Input": [{"Source": {"Target": "Parameter", "Id": "Param50"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param48"}, "VertexIndex": 1, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.8, "Delay": 0.74, "Acceleration": 1.47, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}]}