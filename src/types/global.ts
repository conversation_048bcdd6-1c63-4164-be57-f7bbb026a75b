// 全局类型定义
export interface LogEntry {
  id: string;
  timestamp: Date;
  level: 'info' | 'success' | 'error' | 'warning';
  message: string;
}

export interface AudioCacheItem {
  id: string;
  text: string;
  url: string;
  size: number;
  timestamp: Date;
}

export interface TTSProgress {
  status: string;
  chunkCount: number;
  totalSize: number;
}

export type ConnectionStatus = 'disconnected' | 'connecting' | 'connected' | 'error';

export type Live2DModelKey = 'idol' | 'lanhei';