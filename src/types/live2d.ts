// Live2D相关类型定义

export interface Live2DExpression {
  name: string;
  file: string;
}

export interface Live2DMotion {
  name: string;
  file: string;
}

export interface Live2DModel {
  id: string;
  name: string;
  description: string;
  modelPath: string;
  previewImage: string | null;
  expressions: Live2DExpression[];
  motions: Live2DMotion[];
}

export interface Live2DConfig {
  models: Live2DModel[];
  defaultModel: string;
  version: string;
}

export interface Live2DModelState {
  isLoaded: boolean;
  isLoading: boolean;
  currentModelId: string | null;
  currentExpression: string | null;
  currentMotion: string | null;
  status: 'idle' | 'initializing' | 'loading' | 'ready' | 'error';
  error: string | null;
  lastInteraction: number | null;
}

// 模型文件结构
export interface Live2DModelFileStructure {
  Version: number;
  FileReferences: {
    Moc: string;
    Textures: string[];
    Physics?: string;
    DisplayInfo?: string;
    Expressions?: string[];
    Motions?: Record<string, any[]>;
  };
  Groups?: Array<{
    Target: string;
    Name: string;
    Ids: string[];
  }>;
  HitAreas?: Array<{
    Name: string;
    Id: string;
  }>;
  Layout?: Record<string, any>;
}

// 模型加载选项
export interface Live2DLoadOptions {
  useCache?: boolean;
  scale?: number;
  position?: { x: number; y: number };
  anchor?: { x: number; y: number };
}

// 模型显示选项
export interface Live2DDisplayOptions {
  scale?: number;
  x?: number;
  y?: number;
  anchorX?: number;
  anchorY?: number;
}

// 模型验证结果
export interface Live2DValidationResult {
  isValid: boolean;
  missingFiles: string[];
  errors: string[];
}

// CDN配置
export interface Live2DCDNConfig {
  baseUrl: string;
  fallbackUrls: string[];
  timeout: number;
}

// 模型资源管理器接口
export interface Live2DResourceManager {
  loadModel(modelId: string, options?: Live2DLoadOptions): Promise<any>;
  validateModel(modelId: string): Promise<Live2DValidationResult>;
  getModelStructure(modelPath: string): Promise<Live2DModelFileStructure>;
  clearCache(): void;
}

// 全局Live2D类型声明
declare global {
  interface Window {
    PIXI: any;
  }
}