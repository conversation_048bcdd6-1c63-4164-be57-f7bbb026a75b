import { useState, useEffect, useCallback, useRef } from 'react';
import { Live2DService } from '@/services/live2d/Live2DService';
import { EyeTrackingService } from '@/services/live2d/EyeTrackingService';
import { LipSyncService } from '@/services/live2d/LipSyncService';
import { EmotionSystem } from '@/services/live2d/EmotionSystem';
import type { Live2DEventCallbacks } from '@/services/live2d/Live2DService';
import type { Live2DModel, Live2DModelState } from '@/types/live2d';
import type { EyeTrackingOptions, EyeTrackingState } from '@/services/live2d/EyeTrackingService';
import type { LipSyncOptions, LipSyncState } from '@/services/live2d/LipSyncService';
import type { EmotionSystemOptions, EmotionState } from '@/services/live2d/EmotionSystem';

export interface UseLive2DOptions {
  autoLoadDefault?: boolean;
  enableInteraction?: boolean;
  enableMouseTracking?: boolean;
  enableEyeTracking?: boolean;
  enableLipSync?: boolean;
  enableEmotionSystem?: boolean;
  eyeTrackingOptions?: Partial<EyeTrackingOptions>;
  lipSyncOptions?: Partial<LipSyncOptions>;
  emotionOptions?: Partial<EmotionSystemOptions>;
}

export interface UseLive2DReturn {
  // 基础状态
  state: Live2DModelState;
  availableModels: Live2DModel[];
  currentModelExpressions: string[];
  currentModelMotions: { [group: string]: string[] };

  // 增强功能状态
  eyeTrackingState: EyeTrackingState | null;
  lipSyncState: LipSyncState | null;
  emotionState: EmotionState | null;

  // 基础操作方法
  initialize: (container: HTMLElement) => Promise<void>;
  loadModel: (modelId: string) => Promise<void>;
  switchToModel: (modelId: string) => Promise<void>;
  playExpression: (expressionId: string) => Promise<void>;
  playMotion: (motionGroup: string, motionIndex?: number) => Promise<void>;
  resetModel: () => Promise<void>;
  destroy: () => void;

  // 增强功能方法
  updateMousePosition: (x: number, y: number) => void;
  triggerBlink: () => void;
  lookAt: (x: number, y: number) => void;
  lookCenter: () => void;
  setMouthOpen: (value: number) => void;
  simulateSpeaking: (duration?: number, intensity?: number) => void;
  speakText: (text: string, speechRate?: number) => Promise<void>;
  analyzeAndPlayEmotion: (text: string) => Promise<void>;
  playEmotion: (emotion: string) => Promise<void>;

  // 配置方法
  updateEyeTrackingOptions: (options: Partial<EyeTrackingOptions>) => void;
  updateLipSyncOptions: (options: Partial<LipSyncOptions>) => void;
  updateEmotionOptions: (options: Partial<EmotionSystemOptions>) => void;

  // 事件处理
  setEventCallbacks: (callbacks: Partial<Live2DEventCallbacks>) => void;
}

export function useLive2D(options: UseLive2DOptions = {}): UseLive2DReturn {
  // 基础状态
  const [state, setState] = useState<Live2DModelState>({
    isLoaded: false,
    isLoading: false,
    currentModelId: null,
    currentExpression: null,
    currentMotion: null,
    status: 'idle',
    error: null,
    lastInteraction: null
  });

  const [availableModels, setAvailableModels] = useState<Live2DModel[]>([]);
  const [currentModelExpressions, setCurrentModelExpressions] = useState<string[]>([]);
  const [currentModelMotions, setCurrentModelMotions] = useState<{ [group: string]: string[] }>({});

  // 增强功能状态
  const [eyeTrackingState, setEyeTrackingState] = useState<EyeTrackingState | null>(null);
  const [lipSyncState, setLipSyncState] = useState<LipSyncState | null>(null);
  const [emotionState, setEmotionState] = useState<EmotionState | null>(null);

  // 服务实例
  const live2dServiceRef = useRef<Live2DService | null>(null);
  const eyeTrackingServiceRef = useRef<EyeTrackingService | null>(null);
  const lipSyncServiceRef = useRef<LipSyncService | null>(null);
  const emotionSystemRef = useRef<EmotionSystem | null>(null);

  // 容器引用
  const containerRef = useRef<HTMLElement | null>(null);

  // 初始化服务
  useEffect(() => {
    if (!live2dServiceRef.current) {
      live2dServiceRef.current = Live2DService.getInstance();
    }

    if (options.enableEyeTracking && !eyeTrackingServiceRef.current) {
      eyeTrackingServiceRef.current = new EyeTrackingService(options.eyeTrackingOptions);
    }

    if (options.enableLipSync && !lipSyncServiceRef.current) {
      lipSyncServiceRef.current = new LipSyncService(options.lipSyncOptions);
    }

    if (options.enableEmotionSystem && !emotionSystemRef.current) {
      emotionSystemRef.current = new EmotionSystem(options.emotionOptions);
    }

    return () => {
      // 不在这里销毁，在组件卸载时销毁
    };
  }, []);

  // 状态更新定时器
  useEffect(() => {
    const updateStates = () => {
      if (eyeTrackingServiceRef.current) {
        setEyeTrackingState(eyeTrackingServiceRef.current.getState());
      }
      if (lipSyncServiceRef.current) {
        setLipSyncState(lipSyncServiceRef.current.getState());
      }
      if (emotionSystemRef.current) {
        setEmotionState(emotionSystemRef.current.getState());
      }
    };

    const interval = setInterval(updateStates, 100); // 每100ms更新一次状态
    return () => clearInterval(interval);
  }, []);

  // 初始化
  const initialize = useCallback(async (container: HTMLElement) => {
    if (!live2dServiceRef.current) return;

    containerRef.current = container;

    try {
      setState(prev => ({ ...prev, isLoading: true, status: 'initializing' }));

      await live2dServiceRef.current.initialize(container);
      const models = await live2dServiceRef.current.getAvailableModels();
      setAvailableModels(models);

      setState(prev => ({
        ...prev,
        isLoading: false,
        status: 'ready'
      }));

      // 自动加载默认模型
      if (options.autoLoadDefault && models.length > 0) {
        // 延迟加载默认模型，避免循环依赖
        setTimeout(async () => {
          try {
            const defaultModelId = models[0].id;
            console.log(`自动加载默认模型: ${defaultModelId}`);

            // 直接调用服务而不是通过 hook
            const model = await live2dServiceRef.current!.loadModel(defaultModelId);
            if (model) {
              console.log(`默认模型加载成功: ${defaultModelId}`);
            }
          } catch (error) {
            console.error('默认模型加载失败:', error);
          }
        }, 100);
      }

    } catch (error) {
      console.error('Live2D初始化失败:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : '初始化失败',
        status: 'error'
      }));
    }
  }, [options.autoLoadDefault]);

  // 加载模型
  const loadModel = useCallback(async (modelId: string) => {
    if (!live2dServiceRef.current) return;

    try {
      setState(prev => ({ ...prev, isLoading: true, status: 'loading' }));

      const model = await live2dServiceRef.current.loadModel(modelId);

      if (model) {
        // 获取模型配置
        const modelConfig = live2dServiceRef.current.getModelById(modelId);
        if (modelConfig) {
          setCurrentModelExpressions(modelConfig.expressions.map(exp => exp.name));
          setCurrentModelMotions({
            idle: modelConfig.motions.filter(m => m.name.includes('idle')).map(m => m.name),
            tap: modelConfig.motions.filter(m => m.name.includes('tap')).map(m => m.name),
            other: modelConfig.motions.filter(m => !m.name.includes('idle') && !m.name.includes('tap')).map(m => m.name)
          });
        }

        // 设置增强功能
        if (eyeTrackingServiceRef.current) {
          eyeTrackingServiceRef.current.setModel(model);
        }

        if (lipSyncServiceRef.current) {
          lipSyncServiceRef.current.setModel(model);
          await lipSyncServiceRef.current.startLipSync();
        }

        if (emotionSystemRef.current) {
          emotionSystemRef.current.setModel(model, async (expression: string) => {
            await playExpression(expression);
          });
        }

        setState(prev => ({
          ...prev,
          isLoaded: true,
          isLoading: false,
          currentModelId: modelId,
          status: 'ready',
          error: null
        }));
      } else {
        throw new Error('模型加载失败');
      }

    } catch (error) {
      console.error('模型加载失败:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : '模型加载失败',
        status: 'error'
      }));
    }
  }, []);

  // 切换模型
  const switchToModel = useCallback(async (modelId: string) => {
    await loadModel(modelId);
  }, [loadModel]);

  // 播放表情
  const playExpression = useCallback(async (expressionId: string) => {
    if (!live2dServiceRef.current) return;

    try {
      await live2dServiceRef.current.playExpression(expressionId);
      setState(prev => ({
        ...prev,
        currentExpression: expressionId,
        lastInteraction: Date.now()
      }));
    } catch (error) {
      console.error('播放表情失败:', error);
    }
  }, []);

  // 播放动作
  const playMotion = useCallback(async (motionGroup: string, motionIndex?: number) => {
    if (!live2dServiceRef.current) return;

    try {
      await live2dServiceRef.current.playMotion(motionGroup, motionIndex);
      setState(prev => ({
        ...prev,
        currentMotion: `${motionGroup}_${motionIndex || 0}`,
        lastInteraction: Date.now()
      }));
    } catch (error) {
      console.error('播放动作失败:', error);
    }
  }, []);

  // 重置模型
  const resetModel = useCallback(async () => {
    if (!live2dServiceRef.current) return;

    try {
      await live2dServiceRef.current.resetModel();
      setState(prev => ({
        ...prev,
        currentExpression: null,
        currentMotion: null,
        lastInteraction: Date.now()
      }));
    } catch (error) {
      console.error('重置模型失败:', error);
    }
  }, []);

  // 销毁
  const destroy = useCallback(() => {
    if (eyeTrackingServiceRef.current) {
      eyeTrackingServiceRef.current.destroy();
      eyeTrackingServiceRef.current = null;
    }

    if (lipSyncServiceRef.current) {
      lipSyncServiceRef.current.destroy();
      lipSyncServiceRef.current = null;
    }

    if (emotionSystemRef.current) {
      emotionSystemRef.current.destroy();
      emotionSystemRef.current = null;
    }

    setState({
      isLoaded: false,
      isLoading: false,
      currentModelId: null,
      currentExpression: null,
      currentMotion: null,
      status: 'idle',
      error: null,
      lastInteraction: null
    });
  }, []);

  // 更新鼠标位置
  const updateMousePosition = useCallback((x: number, y: number) => {
    if (eyeTrackingServiceRef.current && containerRef.current) {
      const rect = containerRef.current.getBoundingClientRect();
      eyeTrackingServiceRef.current.updateMousePosition(
        x - rect.left,
        y - rect.top,
        rect.width,
        rect.height
      );
    }
  }, []);

  // 触发眨眼
  const triggerBlink = useCallback(() => {
    eyeTrackingServiceRef.current?.triggerBlink();
  }, []);

  // 注视指定位置
  const lookAt = useCallback((x: number, y: number) => {
    eyeTrackingServiceRef.current?.lookAt(x, y);
  }, []);

  // 回到中心位置
  const lookCenter = useCallback(() => {
    eyeTrackingServiceRef.current?.lookCenter();
  }, []);

  // 设置嘴部开合度
  const setMouthOpen = useCallback((value: number) => {
    lipSyncServiceRef.current?.setMouthOpen(value);
  }, []);

  // 模拟说话
  const simulateSpeaking = useCallback((duration?: number, intensity?: number) => {
    lipSyncServiceRef.current?.simulateSpeaking(duration, intensity);
  }, []);

  // 文本转语音
  const speakText = useCallback(async (text: string, speechRate?: number) => {
    if (lipSyncServiceRef.current) {
      await lipSyncServiceRef.current.speakText(text, speechRate);
    }

    // 同时分析情感
    if (emotionSystemRef.current) {
      await emotionSystemRef.current.analyzeAndPlay(text);
    }
  }, []);

  // 分析并播放情感
  const analyzeAndPlayEmotion = useCallback(async (text: string) => {
    if (emotionSystemRef.current) {
      await emotionSystemRef.current.analyzeAndPlay(text);
    }
  }, []);

  // 播放情感
  const playEmotion = useCallback(async (emotion: string) => {
    if (emotionSystemRef.current) {
      await emotionSystemRef.current.playEmotion(emotion);
    }
  }, []);

  // 更新配置
  const updateEyeTrackingOptions = useCallback((newOptions: Partial<EyeTrackingOptions>) => {
    eyeTrackingServiceRef.current?.updateOptions(newOptions);
  }, []);

  const updateLipSyncOptions = useCallback((newOptions: Partial<LipSyncOptions>) => {
    lipSyncServiceRef.current?.updateOptions(newOptions);
  }, []);

  const updateEmotionOptions = useCallback((newOptions: Partial<EmotionSystemOptions>) => {
    emotionSystemRef.current?.updateOptions(newOptions);
  }, []);

  // 设置事件回调
  const setEventCallbacks = useCallback((callbacks: Partial<Live2DEventCallbacks>) => {
    // TODO: 实现事件回调设置
    console.log('设置事件回调:', callbacks);
  }, []);

  return {
    // 基础状态
    state,
    availableModels,
    currentModelExpressions,
    currentModelMotions,

    // 增强功能状态
    eyeTrackingState,
    lipSyncState,
    emotionState,

    // 基础操作方法
    initialize,
    loadModel,
    switchToModel,
    playExpression,
    playMotion,
    resetModel,
    destroy,

    // 增强功能方法
    updateMousePosition,
    triggerBlink,
    lookAt,
    lookCenter,
    setMouthOpen,
    simulateSpeaking,
    speakText,
    analyzeAndPlayEmotion,
    playEmotion,

    // 配置方法
    updateEyeTrackingOptions,
    updateLipSyncOptions,
    updateEmotionOptions,

    // 事件处理
    setEventCallbacks
  };
}
