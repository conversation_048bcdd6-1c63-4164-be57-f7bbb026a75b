import { Live2DModel as PixiLive2DModel } from 'pixi-live2d-display';

export interface LipSyncOptions {
  enabled: boolean;
  sensitivity: number; // 0-1, 嘴部动作灵敏度
  smoothing: number; // 0-1, 平滑程度
  autoClose: boolean; // 是否自动闭嘴
  closeDelay: number; // 自动闭嘴延迟（毫秒）
  minOpenValue: number; // 最小张嘴值
  maxOpenValue: number; // 最大张嘴值
}

export interface LipSyncState {
  isActive: boolean;
  currentMouthOpen: number;
  targetMouthOpen: number;
  audioLevel: number;
  lastActivityTime: number;
}

export interface AudioAnalyzer {
  analyzeAudio: (audioData: Float32Array) => number;
  getFrequencyData: () => number[];
}

/**
 * 口型同步服务
 * 提供音频分析和口型同步功能
 */
export class LipSyncService {
  private model: PixiLive2DModel | null = null;
  private options: LipSyncOptions;
  private state: LipSyncState;
  private animationId: number | null = null;
  private audioContext: AudioContext | null = null;
  private analyser: AnalyserNode | null = null;
  private audioSource: MediaElementAudioSourceNode | null = null;
  private autoCloseTimer: NodeJS.Timeout | null = null;

  // 参数名称映射
  private parameterNames = {
    mouthOpen: ['ParamMouthOpenY', 'PARAM_MOUTH_OPEN_Y', 'MouthOpen'],
    mouthForm: ['ParamMouthForm', 'PARAM_MOUTH_FORM', 'MouthForm'],
    mouthScale: ['ParamMouthScale', 'PARAM_MOUTH_SCALE', 'MouthScale']
  };

  constructor(options: Partial<LipSyncOptions> = {}) {
    this.options = {
      enabled: true,
      sensitivity: 0.8,
      smoothing: 0.6,
      autoClose: true,
      closeDelay: 500, // 500ms后自动闭嘴
      minOpenValue: 0,
      maxOpenValue: 1,
      ...options
    };

    this.state = {
      isActive: false,
      currentMouthOpen: 0,
      targetMouthOpen: 0,
      audioLevel: 0,
      lastActivityTime: 0
    };
  }

  /**
   * 设置Live2D模型
   */
  setModel(model: PixiLive2DModel): void {
    this.model = model;
    this.validateParameters();
  }

  /**
   * 验证模型参数
   */
  private validateParameters(): void {
    if (!this.model) return;

    const mouthParam = this.findParameter(this.parameterNames.mouthOpen);
    if (mouthParam) {
      console.log(`口型同步参数: ${mouthParam}`);
    } else {
      console.warn('未找到口型同步参数');
    }
  }

  /**
   * 查找参数
   */
  private findParameter(candidates: string[]): string | null {
    if (!this.model?.internalModel) return null;
    
    for (const candidate of candidates) {
      try {
        const coreModel = this.model.internalModel.coreModel as any;
        const value = coreModel?.getParameterValueById?.(candidate);
        if (value !== undefined) {
          return candidate;
        }
      } catch (error) {
        // 参数不存在，继续尝试下一个
      }
    }
    
    return null;
  }

  /**
   * 设置参数值
   */
  private setParameter(candidates: string[], value: number): void {
    const paramId = this.findParameter(candidates);
    if (paramId && this.model?.internalModel) {
      try {
        const coreModel = this.model.internalModel.coreModel as any;
        coreModel?.setParameterValueById?.(paramId, value);
      } catch (error) {
        console.warn(`设置参数失败: ${paramId}`, error);
      }
    }
  }

  /**
   * 开始口型同步
   */
  async startLipSync(): Promise<void> {
    if (!this.options.enabled || this.state.isActive) return;

    try {
      await this.initializeAudioContext();
      this.state.isActive = true;
      this.startAnimation();
      console.log('口型同步已启动');
    } catch (error) {
      console.error('口型同步启动失败:', error);
    }
  }

  /**
   * 停止口型同步
   */
  stopLipSync(): void {
    this.state.isActive = false;
    
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }

    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
      this.autoCloseTimer = null;
    }

    // 重置嘴部到闭合状态
    this.setParameter(this.parameterNames.mouthOpen, 0);
    this.state.currentMouthOpen = 0;
    this.state.targetMouthOpen = 0;

    console.log('口型同步已停止');
  }

  /**
   * 初始化音频上下文
   */
  private async initializeAudioContext(): Promise<void> {
    if (this.audioContext) return;

    this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    this.analyser = this.audioContext.createAnalyser();
    
    // 配置分析器
    this.analyser.fftSize = 256;
    this.analyser.smoothingTimeConstant = 0.8;
  }

  /**
   * 连接音频元素
   */
  connectAudioElement(audioElement: HTMLAudioElement): void {
    if (!this.audioContext || !this.analyser) return;

    try {
      // 如果已有音频源，先断开
      if (this.audioSource) {
        this.audioSource.disconnect();
      }

      // 创建新的音频源
      this.audioSource = this.audioContext.createMediaElementSource(audioElement);
      this.audioSource.connect(this.analyser);
      this.analyser.connect(this.audioContext.destination);

      console.log('音频元素已连接到口型同步');
    } catch (error) {
      console.error('连接音频元素失败:', error);
    }
  }

  /**
   * 分析音频数据
   */
  private analyzeAudio(): number {
    if (!this.analyser) return 0;

    const bufferLength = this.analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    this.analyser.getByteFrequencyData(dataArray);

    // 计算低频能量（主要是语音频率）
    let sum = 0;
    const lowFreqEnd = Math.floor(bufferLength * 0.3); // 取前30%的频率
    
    for (let i = 0; i < lowFreqEnd; i++) {
      sum += dataArray[i];
    }

    // 归一化到0-1范围
    const average = sum / lowFreqEnd / 255;
    return Math.min(1, average * this.options.sensitivity);
  }

  /**
   * 启动动画循环
   */
  private startAnimation(): void {
    const animate = () => {
      if (!this.state.isActive) return;

      this.updateLipSync();
      this.animationId = requestAnimationFrame(animate);
    };

    animate();
  }

  /**
   * 更新口型同步
   */
  private updateLipSync(): void {
    if (!this.model) return;

    // 分析音频获取音量级别
    const audioLevel = this.analyzeAudio();
    this.state.audioLevel = audioLevel;

    // 如果有音频活动，更新目标嘴部开合度
    if (audioLevel > 0.01) {
      const mouthOpenValue = this.mapAudioToMouth(audioLevel);
      this.state.targetMouthOpen = mouthOpenValue;
      this.state.lastActivityTime = Date.now();

      // 清除自动闭嘴计时器
      if (this.autoCloseTimer) {
        clearTimeout(this.autoCloseTimer);
        this.autoCloseTimer = null;
      }
    } else {
      // 设置自动闭嘴计时器
      if (this.options.autoClose && !this.autoCloseTimer) {
        this.autoCloseTimer = setTimeout(() => {
          this.state.targetMouthOpen = 0;
          this.autoCloseTimer = null;
        }, this.options.closeDelay);
      }
    }

    // 平滑过渡到目标值
    const smoothing = this.options.smoothing;
    this.state.currentMouthOpen += (this.state.targetMouthOpen - this.state.currentMouthOpen) * (1 - smoothing);

    // 设置嘴部参数
    this.setParameter(this.parameterNames.mouthOpen, this.state.currentMouthOpen);
  }

  /**
   * 将音频级别映射到嘴部开合度
   */
  private mapAudioToMouth(audioLevel: number): number {
    const { minOpenValue, maxOpenValue } = this.options;
    
    // 应用非线性映射，让小声音也有明显的嘴部动作
    const nonlinear = Math.pow(audioLevel, 0.6);
    
    // 映射到指定范围
    return minOpenValue + (maxOpenValue - minOpenValue) * nonlinear;
  }

  /**
   * 手动设置嘴部开合度
   */
  setMouthOpen(value: number): void {
    this.state.targetMouthOpen = Math.max(0, Math.min(1, value));
    this.state.lastActivityTime = Date.now();
  }

  /**
   * 模拟说话动作
   */
  simulateSpeaking(duration: number = 2000, intensity: number = 0.8): void {
    if (!this.state.isActive) return;

    const startTime = Date.now();
    const baseFrequency = 8; // 基础频率（每秒变化次数）
    
    const simulate = () => {
      const elapsed = Date.now() - startTime;
      if (elapsed >= duration) {
        this.state.targetMouthOpen = 0;
        return;
      }

      // 生成类似语音的波形
      const time = elapsed / 1000;
      const wave1 = Math.sin(time * baseFrequency * Math.PI * 2) * 0.5 + 0.5;
      const wave2 = Math.sin(time * baseFrequency * 1.7 * Math.PI * 2) * 0.3 + 0.3;
      const combined = (wave1 + wave2) / 2;
      
      // 添加随机变化
      const random = Math.random() * 0.3;
      const mouthValue = (combined * 0.7 + random * 0.3) * intensity;
      
      this.state.targetMouthOpen = mouthValue;
      this.state.lastActivityTime = Date.now();

      requestAnimationFrame(simulate);
    };

    simulate();
  }

  /**
   * 从文本生成口型同步
   */
  async speakText(text: string, speechRate: number = 1.0): Promise<void> {
    if (!text.trim()) return;

    // 估算说话时间（每个字符约150ms，根据语速调整）
    const baseTimePerChar = 150;
    const timePerChar = baseTimePerChar / speechRate;
    const duration = text.length * timePerChar;

    // 根据文本内容调整强度
    let intensity = 0.7;
    if (text.includes('！') || text.includes('!')) intensity = 0.9;
    if (text.includes('？') || text.includes('?')) intensity = 0.6;
    if (text.includes('，') || text.includes(',')) intensity = 0.5;

    this.simulateSpeaking(duration, intensity);
  }

  /**
   * 连接TTS音频
   */
  async connectTTSAudio(audioBlob: Blob): Promise<void> {
    const audioElement = new Audio();
    audioElement.src = URL.createObjectURL(audioBlob);
    
    // 连接到分析器
    this.connectAudioElement(audioElement);
    
    // 播放音频
    try {
      await audioElement.play();
    } catch (error) {
      console.error('播放TTS音频失败:', error);
    }
  }

  /**
   * 更新选项
   */
  updateOptions(options: Partial<LipSyncOptions>): void {
    this.options = { ...this.options, ...options };
    
    if (!options.enabled && this.state.isActive) {
      this.stopLipSync();
    } else if (options.enabled && !this.state.isActive) {
      this.startLipSync();
    }
  }

  /**
   * 获取当前状态
   */
  getState(): LipSyncState {
    return { ...this.state };
  }

  /**
   * 获取当前选项
   */
  getOptions(): LipSyncOptions {
    return { ...this.options };
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.stopLipSync();
    
    if (this.audioSource) {
      this.audioSource.disconnect();
      this.audioSource = null;
    }
    
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
    
    this.analyser = null;
    this.model = null;
  }
} 