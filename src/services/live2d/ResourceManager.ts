import { 
  Live2DModel, 
  Live2DLoadOptions, 
  Live2DValidationResult, 
  Live2DModelFileStructure,
  Live2DCDNConfig,
  Live2DResourceManager 
} from '@/types/live2d';

/**
 * Live2D资源管理器
 * 负责模型资源的加载、缓存和验证
 */
export class ResourceManager implements Live2DResourceManager {
  private static instance: ResourceManager;
  private modelCache: Map<string, any> = new Map();
  private structureCache: Map<string, Live2DModelFileStructure> = new Map();
  private cdnConfig: Live2DCDNConfig;

  private constructor() {
    this.cdnConfig = {
      baseUrl: 'https://cdn.jsdelivr.net/gh/your-repo/evercall-tts-react@main/public',
      fallbackUrls: [
        'https://unpkg.com/evercall-tts-models@latest',
        'https://cdn.staticaly.com/gh/your-repo/evercall-tts-react/main/public'
      ],
      timeout: 10000
    };
  }

  public static getInstance(): ResourceManager {
    if (!ResourceManager.instance) {
      ResourceManager.instance = new ResourceManager();
    }
    return ResourceManager.instance;
  }

  /**
   * 加载模型
   * @param modelId 模型ID
   * @param options 加载选项
   */
  async loadModel(modelId: string, options: Live2DLoadOptions = {}): Promise<any> {
    const { useCache = true } = options;

    try {
      // 检查缓存
      if (useCache && this.modelCache.has(modelId)) {
        console.log(`从缓存加载模型: ${modelId}`);
        return this.modelCache.get(modelId);
      }

      // 获取模型配置
      const modelConfig = await this.getModelConfig(modelId);
      if (!modelConfig) {
        throw new Error(`模型配置不存在: ${modelId}`);
      }

      // 尝试多个加载源
      const loadSources = this.getLoadSources(modelConfig.modelPath);
      let model = null;
      let lastError = null;

      for (const source of loadSources) {
        try {
          console.log(`尝试从 ${source} 加载模型...`);
          model = await this.loadFromSource(source);
          console.log(`模型加载成功: ${modelId} from ${source}`);
          break;
        } catch (error) {
          console.warn(`从 ${source} 加载失败:`, error);
          lastError = error;
        }
      }

      if (!model) {
        throw lastError || new Error(`所有加载源都失败: ${modelId}`);
      }

      // 缓存模型
      if (useCache) {
        this.modelCache.set(modelId, model);
      }

      return model;
    } catch (error) {
      console.error(`模型加载失败: ${modelId}`, error);
      throw error;
    }
  }

  /**
   * 验证模型
   * @param modelId 模型ID
   */
  async validateModel(modelId: string): Promise<Live2DValidationResult> {
    const result: Live2DValidationResult = {
      isValid: true,
      missingFiles: [],
      errors: []
    };

    try {
      const modelConfig = await this.getModelConfig(modelId);
      if (!modelConfig) {
        result.isValid = false;
        result.errors.push(`模型配置不存在: ${modelId}`);
        return result;
      }

      // 获取模型结构
      const structure = await this.getModelStructure(modelConfig.modelPath);
      
      // 检查必需文件
      const requiredFiles = [
        structure.FileReferences.Moc,
        ...(structure.FileReferences.Textures || [])
      ];

      const basePath = this.getBasePath(modelConfig.modelPath);
      
      for (const file of requiredFiles) {
        const filePath = `${basePath}/${file}`;
        const exists = await this.checkFileExists(filePath);
        
        if (!exists) {
          result.missingFiles.push(filePath);
          result.isValid = false;
        }
      }

      // 检查可选文件
      const optionalFiles = [
        structure.FileReferences.Physics,
        structure.FileReferences.DisplayInfo,
        ...(structure.FileReferences.Expressions || [])
      ].filter(Boolean);

      for (const file of optionalFiles) {
        const filePath = `${basePath}/${file}`;
        const exists = await this.checkFileExists(filePath);
        
        if (!exists) {
          console.warn(`可选文件缺失: ${filePath}`);
        }
      }

    } catch (error) {
      result.isValid = false;
      result.errors.push(`验证过程出错: ${error.message}`);
    }

    return result;
  }

  /**
   * 获取模型结构
   * @param modelPath 模型文件路径
   */
  async getModelStructure(modelPath: string): Promise<Live2DModelFileStructure> {
    // 检查缓存
    if (this.structureCache.has(modelPath)) {
      return this.structureCache.get(modelPath)!;
    }

    try {
      const loadSources = this.getLoadSources(modelPath);
      let structure = null;
      let lastError = null;

      for (const source of loadSources) {
        try {
          const response = await this.fetchWithTimeout(source);
          if (response.ok) {
            structure = await response.json();
            break;
          }
        } catch (error) {
          lastError = error;
        }
      }

      if (!structure) {
        throw lastError || new Error(`无法加载模型结构: ${modelPath}`);
      }

      // 缓存结构
      this.structureCache.set(modelPath, structure);
      return structure;
    } catch (error) {
      console.error(`模型结构获取失败: ${modelPath}`, error);
      throw error;
    }
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.modelCache.clear();
    this.structureCache.clear();
    console.log('Live2D资源缓存已清除');
  }

  /**
   * 获取模型配置
   */
  private async getModelConfig(modelId: string): Promise<Live2DModel | null> {
    try {
      const response = await fetch('/static/live2d/config/models.json');
      if (!response.ok) {
        throw new Error(`配置文件加载失败: ${response.status}`);
      }
      
      const config = await response.json();
      return config.models.find((model: Live2DModel) => model.id === modelId) || null;
    } catch (error) {
      console.error('模型配置获取失败:', error);
      return null;
    }
  }

  /**
   * 获取加载源列表
   */
  private getLoadSources(modelPath: string): string[] {
    const sources = [modelPath]; // 本地路径

    // 添加CDN源
    sources.push(`${this.cdnConfig.baseUrl}${modelPath}`);
    
    // 添加备用CDN源
    for (const fallbackUrl of this.cdnConfig.fallbackUrls) {
      sources.push(`${fallbackUrl}${modelPath}`);
    }

    return sources;
  }

  /**
   * 从指定源加载模型
   */
  private async loadFromSource(source: string): Promise<any> {
    if (typeof window.PIXI?.live2d?.Live2DModel?.from !== 'function') {
      throw new Error('Live2D库未正确加载');
    }

    return await window.PIXI.live2d.Live2DModel.from(source);
  }

  /**
   * 检查文件是否存在
   */
  private async checkFileExists(filePath: string): Promise<boolean> {
    const sources = this.getLoadSources(filePath);
    
    for (const source of sources) {
      try {
        const response = await this.fetchWithTimeout(source, { method: 'HEAD' });
        if (response.ok) {
          return true;
        }
      } catch (error) {
        // 继续检查下一个源
      }
    }
    
    return false;
  }

  /**
   * 带超时的fetch请求
   */
  private async fetchWithTimeout(url: string, options: RequestInit = {}): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.cdnConfig.timeout);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });
      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  /**
   * 获取基础路径
   */
  private getBasePath(modelPath: string): string {
    return modelPath.substring(0, modelPath.lastIndexOf('/'));
  }

  /**
   * 预加载模型
   * @param modelIds 要预加载的模型ID列表
   */
  async preloadModels(modelIds: string[]): Promise<void> {
    const loadPromises = modelIds.map(async (modelId) => {
      try {
        await this.loadModel(modelId, { useCache: true });
        console.log(`模型预加载成功: ${modelId}`);
      } catch (error) {
        console.warn(`模型预加载失败: ${modelId}`, error);
      }
    });

    await Promise.allSettled(loadPromises);
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): { modelCount: number; structureCount: number } {
    return {
      modelCount: this.modelCache.size,
      structureCount: this.structureCache.size
    };
  }

  /**
   * 设置CDN配置
   */
  setCDNConfig(config: Partial<Live2DCDNConfig>): void {
    this.cdnConfig = { ...this.cdnConfig, ...config };
  }

  /**
   * 获取CDN配置
   */
  getCDNConfig(): Live2DCDNConfig {
    return { ...this.cdnConfig };
  }
}

export default ResourceManager;