import { Live2DModel as PixiLive2DModel } from 'pixi-live2d-display';

export interface PersonalityTrait {
  name: string;
  value: number; // 0-1, 特质强度
  description: string;
}

export interface VoiceSettings {
  pitch: number; // 0.5-2.0, 音调
  speed: number; // 0.5-2.0, 语速
  volume: number; // 0-1, 音量
  tone: 'friendly' | 'professional' | 'cheerful' | 'calm' | 'energetic';
}

export interface ReactionPattern {
  trigger: string; // 触发条件
  expressions: string[]; // 可能的表情反应
  motions: string[]; // 可能的动作反应
  probability: number; // 0-1, 反应概率
  cooldown: number; // 冷却时间（毫秒）
}

export interface PersonalityProfile {
  id: string;
  name: string;
  description: string;
  traits: PersonalityTrait[];
  voiceSettings: VoiceSettings;
  reactionPatterns: ReactionPattern[];
  greetings: string[];
  farewells: string[];
  idleComments: string[];
  preferences: {
    emotionSensitivity: number; // 情感敏感度
    interactionFrequency: number; // 互动频率
    playfulness: number; // 顽皮程度
    formality: number; // 正式程度
  };
}

export interface PersonalityState {
  currentProfile: PersonalityProfile | null;
  mood: number; // -1到1，当前心情
  energy: number; // 0-1，当前精力
  familiarity: number; // 0-1，与用户的熟悉度
  lastInteractionTime: number;
  reactionHistory: Array<{
    trigger: string;
    reaction: string;
    timestamp: number;
  }>;
}

export interface PersonalitySystemOptions {
  enabled: boolean;
  learningEnabled: boolean; // 是否启用学习功能
  adaptationRate: number; // 适应速度
  moodDecayRate: number; // 心情衰减速度
  energyRecoveryRate: number; // 精力恢复速度
}

/**
 * 角色个性化系统
 * 管理角色的性格、偏好、反应模式等
 */
export class PersonalitySystem {
  private model: PixiLive2DModel | null = null;
  private options: PersonalitySystemOptions;
  private state: PersonalityState;
  private profiles: Map<string, PersonalityProfile>;
  private reactionCooldowns: Map<string, number> = new Map();
  private moodUpdateTimer: NodeJS.Timeout | null = null;
  private playExpressionCallback?: (expression: string) => Promise<void>;
  private playMotionCallback?: (motionGroup: string, motionIndex?: number) => Promise<void>;

  constructor(options: Partial<PersonalitySystemOptions> = {}) {
    this.options = {
      enabled: true,
      learningEnabled: true,
      adaptationRate: 0.1,
      moodDecayRate: 0.01,
      energyRecoveryRate: 0.005,
      ...options
    };

    this.state = {
      currentProfile: null,
      mood: 0,
      energy: 1,
      familiarity: 0,
      lastInteractionTime: 0,
      reactionHistory: []
    };

    this.profiles = new Map();
    this.initializeDefaultProfiles();
    this.startMoodSystem();
  }

  /**
   * 初始化默认性格档案
   */
  private initializeDefaultProfiles(): void {
    const profiles: PersonalityProfile[] = [
      {
        id: 'cheerful',
        name: '开朗活泼',
        description: '性格开朗，充满活力，喜欢与人交流',
        traits: [
          { name: 'extraversion', value: 0.9, description: '外向性' },
          { name: 'agreeableness', value: 0.8, description: '亲和性' },
          { name: 'optimism', value: 0.9, description: '乐观性' },
          { name: 'energy', value: 0.8, description: '活力' }
        ],
        voiceSettings: {
          pitch: 1.2,
          speed: 1.1,
          volume: 0.8,
          tone: 'cheerful'
        },
        reactionPatterns: [
          {
            trigger: 'greeting',
            expressions: ['f02', 'happy', 'smile'],
            motions: ['wave', 'nod'],
            probability: 0.9,
            cooldown: 1000
          },
          {
            trigger: 'compliment',
            expressions: ['f06', 'shy', 'happy'],
            motions: ['bounce', 'twirl'],
            probability: 0.8,
            cooldown: 2000
          }
        ],
        greetings: [
          '你好呀！今天过得怎么样？',
          '嗨！很高兴见到你！',
          '哇，你来了！我等你好久了呢~'
        ],
        farewells: [
          '再见！记得想我哦~',
          '下次见面，我们再聊！',
          '拜拜！要记得照顾好自己！'
        ],
        idleComments: [
          '嗯嗯，今天天气真不错呢~',
          '我在想，我们下次可以聊什么呢？',
          '你知道吗，我最喜欢和你聊天了！'
        ],
        preferences: {
          emotionSensitivity: 0.8,
          interactionFrequency: 0.7,
          playfulness: 0.9,
          formality: 0.2
        }
      },
      {
        id: 'calm',
        name: '沉稳温和',
        description: '性格沉稳，温文尔雅，善于倾听',
        traits: [
          { name: 'stability', value: 0.9, description: '稳定性' },
          { name: 'patience', value: 0.8, description: '耐心' },
          { name: 'wisdom', value: 0.7, description: '智慧' },
          { name: 'composure', value: 0.9, description: '沉着' }
        ],
        voiceSettings: {
          pitch: 0.9,
          speed: 0.8,
          volume: 0.6,
          tone: 'calm'
        },
        reactionPatterns: [
          {
            trigger: 'greeting',
            expressions: ['f01', 'neutral', 'gentle'],
            motions: ['nod', 'bow'],
            probability: 0.7,
            cooldown: 1500
          },
          {
            trigger: 'question',
            expressions: ['f05', 'thinking', 'contemplative'],
            motions: ['think', 'pause'],
            probability: 0.8,
            cooldown: 2000
          }
        ],
        greetings: [
          '你好，欢迎回来。',
          '很高兴再次见到你。',
          '希望你今天一切都好。'
        ],
        farewells: [
          '愿你一切安好，再见。',
          '期待下次相见。',
          '祝你有美好的一天。'
        ],
        idleComments: [
          '静静地等待也是一种美好。',
          '有什么想聊的吗？我很乐意倾听。',
          '时间过得真快，不知不觉又到了这个时候。'
        ],
        preferences: {
          emotionSensitivity: 0.6,
          interactionFrequency: 0.4,
          playfulness: 0.3,
          formality: 0.7
        }
      },
      {
        id: 'shy',
        name: '害羞内向',
        description: '性格内向，有些害羞，但很温柔',
        traits: [
          { name: 'introversion', value: 0.8, description: '内向性' },
          { name: 'sensitivity', value: 0.9, description: '敏感性' },
          { name: 'gentleness', value: 0.9, description: '温柔' },
          { name: 'modesty', value: 0.8, description: '谦逊' }
        ],
        voiceSettings: {
          pitch: 1.1,
          speed: 0.9,
          volume: 0.5,
          tone: 'friendly'
        },
        reactionPatterns: [
          {
            trigger: 'attention',
            expressions: ['f06', 'shy', 'blush'],
            motions: ['fidget', 'look_away'],
            probability: 0.9,
            cooldown: 1000
          },
          {
            trigger: 'praise',
            expressions: ['f06', 'embarrassed', 'shy'],
            motions: ['cover_face', 'blush'],
            probability: 0.8,
            cooldown: 3000
          }
        ],
        greetings: [
          '啊...你好...',
          '嗯，你来了呢...',
          '那个...很高兴见到你...'
        ],
        farewells: [
          '那...那么再见...',
          '下次...下次再聊吧...',
          '要好好照顾自己哦...'
        ],
        idleComments: [
          '嗯...今天感觉有点紧张呢...',
          '不知道说什么好...',
          '和你在一起感觉很安心...'
        ],
        preferences: {
          emotionSensitivity: 0.9,
          interactionFrequency: 0.3,
          playfulness: 0.4,
          formality: 0.6
        }
      }
    ];

    profiles.forEach(profile => {
      this.profiles.set(profile.id, profile);
    });
  }

  /**
   * 设置Live2D模型和回调
   */
  setModel(
    model: PixiLive2DModel,
    playExpressionCallback: (expression: string) => Promise<void>,
    playMotionCallback: (motionGroup: string, motionIndex?: number) => Promise<void>
  ): void {
    this.model = model;
    this.playExpressionCallback = playExpressionCallback;
    this.playMotionCallback = playMotionCallback;
  }

  /**
   * 切换性格档案
   */
  async switchPersonality(profileId: string): Promise<void> {
    const profile = this.profiles.get(profileId);
    if (!profile) {
      throw new Error(`性格档案不存在: ${profileId}`);
    }

    this.state.currentProfile = profile;
    
    // 重置状态
    this.state.mood = 0;
    this.state.energy = 1;
    this.state.familiarity = Math.max(0.1, this.state.familiarity * 0.5); // 保留一些熟悉度
    
    // 播放问候语
    await this.greet();
    
    console.log(`切换到性格: ${profile.name}`);
  }

  /**
   * 处理用户交互
   */
  async handleInteraction(interactionType: string, context?: any): Promise<void> {
    if (!this.options.enabled || !this.state.currentProfile) return;

    this.state.lastInteractionTime = Date.now();
    this.updateFamiliarity(0.01); // 增加熟悉度
    this.updateMood(0.1); // 提升心情
    this.updateEnergy(-0.05); // 消耗精力

    await this.processReaction(interactionType, context);
  }

  /**
   * 处理反应
   */
  private async processReaction(trigger: string, context?: any): Promise<void> {
    if (!this.state.currentProfile) return;

    // 查找匹配的反应模式
    const matchingPatterns = this.state.currentProfile.reactionPatterns.filter(
      pattern => pattern.trigger === trigger
    );

    for (const pattern of matchingPatterns) {
      // 检查冷却时间
      const lastReaction = this.reactionCooldowns.get(pattern.trigger) || 0;
      if (Date.now() - lastReaction < pattern.cooldown) continue;

      // 计算反应概率
      const adjustedProbability = this.calculateReactionProbability(pattern);
      
      if (Math.random() < adjustedProbability) {
        await this.executeReaction(pattern);
        this.reactionCooldowns.set(pattern.trigger, Date.now());
        
        // 记录反应历史
        this.state.reactionHistory.push({
          trigger,
          reaction: pattern.expressions[0] || pattern.motions[0],
          timestamp: Date.now()
        });
        
        break; // 只执行一个反应
      }
    }
  }

  /**
   * 计算反应概率
   */
  private calculateReactionProbability(pattern: ReactionPattern): number {
    if (!this.state.currentProfile) return pattern.probability;

    let probability = pattern.probability;
    
    // 根据心情调整
    probability *= (1 + this.state.mood * 0.2);
    
    // 根据精力调整
    probability *= this.state.energy;
    
    // 根据熟悉度调整
    probability *= (0.5 + this.state.familiarity * 0.5);
    
    // 根据性格特质调整
    const playfulness = this.state.currentProfile.preferences.playfulness;
    probability *= (0.7 + playfulness * 0.3);
    
    return Math.max(0, Math.min(1, probability));
  }

  /**
   * 执行反应
   */
  private async executeReaction(pattern: ReactionPattern): Promise<void> {
    try {
      // 随机选择表情或动作
      if (pattern.expressions.length > 0 && Math.random() < 0.7) {
        const expression = pattern.expressions[Math.floor(Math.random() * pattern.expressions.length)];
        await this.playExpressionCallback?.(expression);
      }
      
      if (pattern.motions.length > 0 && Math.random() < 0.5) {
        const motion = pattern.motions[Math.floor(Math.random() * pattern.motions.length)];
        await this.playMotionCallback?.(motion);
      }
    } catch (error) {
      console.error('执行反应失败:', error);
    }
  }

  /**
   * 问候
   */
  async greet(): Promise<string> {
    if (!this.state.currentProfile) return '';

    const greetings = this.state.currentProfile.greetings;
    const greeting = greetings[Math.floor(Math.random() * greetings.length)];
    
    await this.handleInteraction('greeting');
    
    return greeting;
  }

  /**
   * 告别
   */
  async farewell(): Promise<string> {
    if (!this.state.currentProfile) return '';

    const farewells = this.state.currentProfile.farewells;
    const farewell = farewells[Math.floor(Math.random() * farewells.length)];
    
    await this.handleInteraction('farewell');
    
    return farewell;
  }

  /**
   * 空闲评论
   */
  async getIdleComment(): Promise<string> {
    if (!this.state.currentProfile) return '';

    const comments = this.state.currentProfile.idleComments;
    const comment = comments[Math.floor(Math.random() * comments.length)];
    
    await this.handleInteraction('idle');
    
    return comment;
  }

  /**
   * 更新熟悉度
   */
  private updateFamiliarity(delta: number): void {
    this.state.familiarity = Math.max(0, Math.min(1, this.state.familiarity + delta));
  }

  /**
   * 更新心情
   */
  private updateMood(delta: number): void {
    this.state.mood = Math.max(-1, Math.min(1, this.state.mood + delta));
  }

  /**
   * 更新精力
   */
  private updateEnergy(delta: number): void {
    this.state.energy = Math.max(0, Math.min(1, this.state.energy + delta));
  }

  /**
   * 启动心情系统
   */
  private startMoodSystem(): void {
    this.moodUpdateTimer = setInterval(() => {
      // 心情自然衰减
      this.state.mood *= (1 - this.options.moodDecayRate);
      
      // 精力自然恢复
      this.updateEnergy(this.options.energyRecoveryRate);
      
      // 长时间无交互降低心情
      const timeSinceLastInteraction = Date.now() - this.state.lastInteractionTime;
      if (timeSinceLastInteraction > 30000) { // 30秒
        this.updateMood(-0.01);
      }
    }, 1000); // 每秒更新一次
  }

  /**
   * 获取可用性格档案
   */
  getAvailableProfiles(): PersonalityProfile[] {
    return Array.from(this.profiles.values());
  }

  /**
   * 获取当前状态
   */
  getState(): PersonalityState {
    return { ...this.state };
  }

  /**
   * 获取性格建议
   */
  getPersonalityRecommendation(userPreferences: any): string | null {
    // 根据用户偏好推荐性格
    if (userPreferences.energetic) return 'cheerful';
    if (userPreferences.serious) return 'calm';
    if (userPreferences.gentle) return 'shy';
    
    return 'cheerful'; // 默认推荐
  }

  /**
   * 学习用户偏好
   */
  learnFromInteraction(interactionType: string, userResponse: 'positive' | 'negative' | 'neutral'): void {
    if (!this.options.learningEnabled || !this.state.currentProfile) return;

    // 简单的学习机制：调整反应概率
    const patterns = this.state.currentProfile.reactionPatterns.filter(
      p => p.trigger === interactionType
    );

    patterns.forEach(pattern => {
      if (userResponse === 'positive') {
        pattern.probability = Math.min(1, pattern.probability * 1.1);
      } else if (userResponse === 'negative') {
        pattern.probability = Math.max(0.1, pattern.probability * 0.9);
      }
    });
  }

  /**
   * 更新选项
   */
  updateOptions(options: Partial<PersonalitySystemOptions>): void {
    this.options = { ...this.options, ...options };
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    if (this.moodUpdateTimer) {
      clearInterval(this.moodUpdateTimer);
      this.moodUpdateTimer = null;
    }

    this.model = null;
    this.playExpressionCallback = undefined;
    this.playMotionCallback = undefined;
    this.reactionCooldowns.clear();
    this.state.reactionHistory = [];
  }
} 