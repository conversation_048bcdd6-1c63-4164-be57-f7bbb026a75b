import { Live2DModel, Live2DExpression, Live2DMotion } from '@/types/live2d';

/**
 * Live2D模型配置适配器
 * 用于解析和转换不同格式的模型配置
 */
export class ModelConfigAdapter {
  /**
   * 从模型文件解析配置信息
   * @param modelPath 模型文件路径
   * @param modelId 模型ID
   */
  static async parseFromModelFile(modelPath: string, modelId: string): Promise<Live2DModel> {
    try {
      const response = await fetch(modelPath);
      if (!response.ok) {
        throw new Error(`模型文件加载失败: ${response.status}`);
      }

      const modelData = await response.json();
      const basePath = modelPath.substring(0, modelPath.lastIndexOf('/'));

      // 解析表情文件
      const expressions = await this.parseExpressions(basePath, modelData);
      
      // 解析动作文件
      const motions = await this.parseMotions(basePath, modelData);

      // 查找预览图片
      const previewImage = await this.findPreviewImage(basePath);

      return {
        id: modelId,
        name: this.extractModelName(modelPath),
        description: `Live2D模型 - ${modelId}`,
        modelPath,
        previewImage,
        expressions,
        motions
      };
    } catch (error) {
      console.error(`模型配置解析失败: ${modelPath}`, error);
      throw error;
    }
  }

  /**
   * 解析表情文件
   */
  private static async parseExpressions(basePath: string, modelData: any): Promise<Live2DExpression[]> {
    const expressions: Live2DExpression[] = [];

    try {
      // 尝试从目录中查找表情文件
      const expressionFiles = await this.findFilesInDirectory(basePath, '.exp3.json');
      
      for (const file of expressionFiles) {
        const name = this.extractFileName(file);
        expressions.push({
          name: this.formatExpressionName(name),
          file
        });
      }

      // 如果模型数据中包含表情定义，使用它们
      if (modelData.FileReferences?.Expressions) {
        for (const expFile of modelData.FileReferences.Expressions) {
          const name = this.extractFileName(expFile);
          if (!expressions.find(e => e.file === expFile)) {
            expressions.push({
              name: this.formatExpressionName(name),
              file: expFile
            });
          }
        }
      }
    } catch (error) {
      console.warn('表情文件解析失败:', error);
    }

    return expressions;
  }

  /**
   * 解析动作文件
   */
  private static async parseMotions(basePath: string, modelData: any): Promise<Live2DMotion[]> {
    const motions: Live2DMotion[] = [];

    try {
      // 尝试从目录中查找动作文件
      const motionFiles = await this.findFilesInDirectory(basePath, '.motion3.json');
      
      for (const file of motionFiles) {
        const name = this.extractFileName(file);
        motions.push({
          name: this.formatMotionName(name),
          file
        });
      }

      // 如果模型数据中包含动作定义，使用它们
      if (modelData.FileReferences?.Motions) {
        for (const [group, motionList] of Object.entries(modelData.FileReferences.Motions)) {
          if (Array.isArray(motionList)) {
            for (let i = 0; i < motionList.length; i++) {
              const motionFile = motionList[i];
              if (typeof motionFile === 'object' && motionFile.File) {
                motions.push({
                  name: `${group}_${i + 1}`,
                  file: motionFile.File
                });
              }
            }
          }
        }
      }
    } catch (error) {
      console.warn('动作文件解析失败:', error);
    }

    return motions;
  }

  /**
   * 查找目录中的文件
   */
  private static async findFilesInDirectory(basePath: string, extension: string): Promise<string[]> {
    const files: string[] = [];
    
    try {
      // 这里需要实现目录扫描逻辑
      // 由于浏览器限制，我们使用已知的文件模式来推测
      const commonPatterns = [
        '1', '2', '3', '4', '5', '6', '7', '8', '9', '10',
        'idle', 'tap', 'flick', 'shake', 'motion',
        'bangbangtang', 'changge', 'dayouxi', 'heilian', 'heiyi',
        'ku', 'lianhong', 'quanquan', 'shengqi', 'shoubiao', 'xingxing',
        'Scene1'
      ];

      for (const pattern of commonPatterns) {
        const fileName = `${pattern}${extension}`;
        try {
          const response = await fetch(`${basePath}/${fileName}`, { method: 'HEAD' });
          if (response.ok) {
            files.push(fileName);
          }
        } catch (error) {
          // 文件不存在，忽略
        }
      }
    } catch (error) {
      console.warn('目录扫描失败:', error);
    }

    return files;
  }

  /**
   * 查找预览图片
   */
  private static async findPreviewImage(basePath: string): Promise<string | null> {
    const imageExtensions = ['.png', '.jpg', '.jpeg'];
    const commonNames = ['preview', 'thumbnail', 'icon', '111'];

    for (const name of commonNames) {
      for (const ext of imageExtensions) {
        const imagePath = `${basePath}/${name}${ext}`;
        try {
          const response = await fetch(imagePath, { method: 'HEAD' });
          if (response.ok) {
            return imagePath;
          }
        } catch (error) {
          // 图片不存在，继续查找
        }
      }
    }

    return null;
  }

  /**
   * 提取模型名称
   */
  private static extractModelName(modelPath: string): string {
    const pathParts = modelPath.split('/');
    const fileName = pathParts[pathParts.length - 1];
    const modelName = fileName.replace('.model3.json', '');
    
    // 格式化名称
    return modelName.charAt(0).toUpperCase() + modelName.slice(1);
  }

  /**
   * 提取文件名（不含扩展名）
   */
  private static extractFileName(filePath: string): string {
    const fileName = filePath.split('/').pop() || filePath;
    return fileName.replace(/\.[^/.]+$/, '');
  }

  /**
   * 格式化表情名称
   */
  private static formatExpressionName(name: string): string {
    const expressionMap: Record<string, string> = {
      '1': '表情1',
      '2': '表情2',
      '3': '表情3',
      '4': '表情4',
      '5': '表情5',
      '6': '表情6',
      '7': '表情7',
      '8': '表情8',
      'bangbangtang': '棒棒糖',
      'changge': '唱歌',
      'dayouxi': '打游戏',
      'heilian': '黑脸',
      'heiyi': '黑衣',
      'ku': '哭',
      'lianhong': '脸红',
      'quanquan': '圈圈',
      'shengqi': '生气',
      'shoubiao': '手表',
      'xingxing': '星星'
    };

    return expressionMap[name] || name;
  }

  /**
   * 格式化动作名称
   */
  private static formatMotionName(name: string): string {
    const motionMap: Record<string, string> = {
      '1': '动作1',
      'Scene1': '场景1',
      'idle': '待机',
      'tap': '点击',
      'flick': '轻拂',
      'shake': '摇摆'
    };

    return motionMap[name] || name;
  }

  /**
   * 验证模型配置
   */
  static validateModelConfig(model: Live2DModel): boolean {
    if (!model.id || !model.name || !model.modelPath) {
      return false;
    }

    // 检查路径格式
    if (!model.modelPath.endsWith('.model3.json')) {
      return false;
    }

    return true;
  }

  /**
   * 转换为标准配置格式
   */
  static normalizeModelConfig(model: Partial<Live2DModel>): Live2DModel {
    return {
      id: model.id || '',
      name: model.name || 'Unknown Model',
      description: model.description || '',
      modelPath: model.modelPath || '',
      previewImage: model.previewImage || null,
      expressions: model.expressions || [],
      motions: model.motions || []
    };
  }

  /**
   * 从现有配置更新模型信息
   */
  static async updateModelFromFile(existingModel: Live2DModel): Promise<Live2DModel> {
    try {
      const updatedModel = await this.parseFromModelFile(existingModel.modelPath, existingModel.id);
      
      // 保留现有的自定义信息
      return {
        ...updatedModel,
        name: existingModel.name || updatedModel.name,
        description: existingModel.description || updatedModel.description
      };
    } catch (error) {
      console.error('模型更新失败:', error);
      return existingModel;
    }
  }

  /**
   * 批量解析模型配置
   */
  static async parseMultipleModels(modelPaths: Array<{ path: string; id: string }>): Promise<Live2DModel[]> {
    const models: Live2DModel[] = [];
    
    for (const { path, id } of modelPaths) {
      try {
        const model = await this.parseFromModelFile(path, id);
        models.push(model);
      } catch (error) {
        console.error(`模型解析失败: ${id}`, error);
        // 创建一个基本的模型配置作为后备
        models.push({
          id,
          name: id,
          description: `模型加载失败: ${id}`,
          modelPath: path,
          previewImage: null,
          expressions: [],
          motions: []
        });
      }
    }

    return models;
  }
}

export default ModelConfigAdapter;