# Live2D模型资源管理系统

这个模块提供了完整的Live2D模型资源管理功能，支持本地和CDN加载，包含模型配置适配器和资源管理器。

## 功能特性

- ✅ 支持Live2D官方模型格式
- ✅ 本地和CDN多源加载
- ✅ 模型配置自动解析
- ✅ 资源缓存管理
- ✅ 模型文件验证
- ✅ TypeScript类型支持
- ✅ React Hook集成

## 目录结构

```
src/services/live2d/
├── Live2DService.ts          # 主要服务类
├── ModelConfigAdapter.ts     # 模型配置适配器
├── ResourceManager.ts        # 资源管理器
└── README.md                 # 说明文档

src/hooks/
└── useLive2D.ts              # React Hook

src/types/
└── live2d.ts                 # TypeScript类型定义

public/static/live2d/
├── config/
│   └── models.json           # 模型配置文件
└── models/                   # 模型文件目录
    ├── idol/                 # 偶像模型
    ├── lanhei/               # 蓝黑模型
    ├── haru/                 # Haru官方模型
    ├── mark/                 # Mark官方模型
    └── natori/               # Natori官方模型
```

## 快速开始

### 1. 基本使用

```typescript
import { useLive2D } from '@/hooks/useLive2D';

function Live2DComponent() {
  const {
    currentModel,
    isLoading,
    error,
    availableModels,
    initializeContainer,
    loadModel,
    playExpression,
    toggleExpression,
    resetModel
  } = useLive2D();

  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (containerRef.current) {
      initializeContainer(containerRef.current);
    }
  }, [initializeContainer]);

  const handleLoadModel = async () => {
    await loadModel('idol', {
      scale: 0.3,
      position: { x: 0.5, y: 0.9 },
      anchor: { x: 0.5, y: 1 }
    });
  };

  return (
    <div>
      <div ref={containerRef} style={{ width: 400, height: 600 }} />
      <button onClick={handleLoadModel} disabled={isLoading}>
        {isLoading ? '加载中...' : '加载模型'}
      </button>
      <button onClick={toggleExpression} disabled={!currentModel}>
        切换表情
      </button>
      <button onClick={resetModel} disabled={!currentModel}>
        重置模型
      </button>
    </div>
  );
}
```

### 2. 直接使用服务类

```typescript
import { Live2DService } from '@/services/live2d/Live2DService';
import { ResourceManager } from '@/services/live2d/ResourceManager';

// 初始化服务
const service = Live2DService.getInstance();
const resourceManager = ResourceManager.getInstance();

// 初始化容器
await service.initialize(containerElement);

// 加载模型
const model = await resourceManager.loadModel('idol');
service.displayModel(model);

// 播放表情
await service.playExpression('f01.exp3.json');

// 播放动作
await service.playMotion('idle', 0);
```

## API 参考

### Live2DService

主要的Live2D服务类，负责模型的显示和控制。

#### 方法

- `getInstance()`: 获取单例实例
- `initialize(container)`: 初始化PIXI应用
- `getAvailableModels()`: 获取可用模型列表
- `getModelById(id)`: 根据ID获取模型配置
- `loadModel(modelId, useCache)`: 加载指定模型
- `displayModel(model, options)`: 显示模型
- `playExpression(expressionName)`: 播放表情
- `playMotion(motionGroup, motionIndex)`: 播放动作
- `resetModel()`: 重置模型状态
- `destroy()`: 销毁服务

### ResourceManager

资源管理器，负责模型文件的加载、缓存和验证。

#### 方法

- `getInstance()`: 获取单例实例
- `loadModel(modelId, options)`: 加载模型
- `validateModel(modelId)`: 验证模型文件
- `getModelStructure(modelPath)`: 获取模型结构
- `clearCache()`: 清除缓存
- `preloadModels(modelIds)`: 预加载模型
- `getCacheStats()`: 获取缓存统计

### ModelConfigAdapter

模型配置适配器，用于解析和转换模型配置。

#### 方法

- `parseFromModelFile(modelPath, modelId)`: 从模型文件解析配置
- `validateModelConfig(model)`: 验证模型配置
- `normalizeModelConfig(model)`: 标准化模型配置
- `updateModelFromFile(existingModel)`: 更新模型配置
- `parseMultipleModels(modelPaths)`: 批量解析模型

### useLive2D Hook

React Hook，提供完整的Live2D功能。

#### 返回值

```typescript
{
  // 状态
  currentModel: Live2DModel | null;
  isLoading: boolean;
  error: string | null;
  currentExpression: string | null;
  isPlaying: boolean;
  availableModels: Live2DModel[];
  
  // 方法
  initializeContainer: (container: HTMLElement) => Promise<void>;
  loadModel: (modelId: string, options?: Live2DLoadOptions) => Promise<void>;
  playExpression: (expressionName: string) => Promise<void>;
  playMotion: (motionGroup: string, motionIndex?: number) => Promise<void>;
  resetModel: () => void;
  toggleExpression: () => void;
  playRandomMotion: () => void;
  validateModel: (modelId: string) => Promise<Live2DValidationResult>;
  
  // 工具属性
  isModelLoaded: boolean;
  hasExpressions: boolean;
  hasMotions: boolean;
}
```

## 模型配置

### 配置文件格式

```json
{
  "models": [
    {
      "id": "model_id",
      "name": "模型名称",
      "description": "模型描述",
      "modelPath": "/static/live2d/models/model_id/model.model3.json",
      "previewImage": "/static/live2d/models/model_id/preview.png",
      "expressions": [
        { "name": "表情名称", "file": "expression.exp3.json" }
      ],
      "motions": [
        { "name": "动作名称", "file": "motion.motion3.json" }
      ]
    }
  ],
  "defaultModel": "model_id",
  "version": "2.0.0"
}
```

### 模型文件结构

```
models/model_id/
├── model.model3.json         # 主模型文件
├── model.moc3                # MOC文件
├── model.physics3.json       # 物理文件
├── model.cdi3.json          # 显示信息文件
├── textures/                 # 纹理目录
│   ├── texture_00.png
│   └── texture_01.png
├── expressions/              # 表情目录
│   ├── f01.exp3.json
│   └── f02.exp3.json
└── motions/                  # 动作目录
    ├── idle.motion3.json
    └── tap.motion3.json
```

## 工具脚本

### 下载官方模型

```bash
# 下载所有官方模型
node scripts/download-models.js download

# 下载指定模型
node scripts/download-models.js download haru

# 验证模型文件
node scripts/download-models.js validate

# 显示模型信息
node scripts/download-models.js info
```

### 测试模型集成

```bash
# 运行完整测试
node scripts/test-live2d.cjs
```

## 支持的模型

### 现有模型

1. **Idol** - 偶像角色
   - 8个表情
   - 1个动作
   - 本地模型

2. **蓝黑** - 蓝黑色调角色
   - 11个表情
   - 1个动作
   - 本地模型

### Live2D官方模型

3. **Haru** - 春日少女
   - 8个表情
   - 4个动作
   - 官方示例模型

4. **Mark** - 马克
   - 7个表情
   - 4个动作
   - 官方示例模型

5. **Natori** - 名取
   - 6个表情
   - 4个动作
   - 官方示例模型

## 许可证说明

- 本地模型（Idol、蓝黑）：项目自有
- 官方模型（Haru、Mark、Natori）：仅供学习使用，遵循Live2D官方许可证

## 故障排除

### 常见问题

1. **模型加载失败**
   - 检查模型文件路径是否正确
   - 验证模型文件完整性
   - 确认网络连接（CDN加载）

2. **表情/动作播放失败**
   - 检查表情/动作文件是否存在
   - 验证文件格式是否正确
   - 确认模型已正确加载

3. **PIXI.js相关错误**
   - 确认PIXI.js库已正确加载
   - 检查Live2D插件版本兼容性
   - 验证容器元素是否存在

### 调试方法

```typescript
// 启用详细日志
console.log('可用模型:', service.getAvailableModels());

// 验证模型
const validation = await resourceManager.validateModel('model_id');
console.log('验证结果:', validation);

// 检查缓存状态
const stats = resourceManager.getCacheStats();
console.log('缓存统计:', stats);
```

## 更新日志

### v2.0.0
- ✅ 完整的Live2D模型资源管理系统
- ✅ 支持本地和CDN多源加载
- ✅ 模型配置自动解析和适配
- ✅ React Hook集成
- ✅ TypeScript类型支持
- ✅ 官方模型集成支持