import { Live2DModel as PixiLive2DModel } from 'pixi-live2d-display';

export interface EyeTrackingOptions {
  enabled: boolean;
  sensitivity: number; // 0-1, 灵敏度
  smoothing: number; // 0-1, 平滑程度
  maxAngle: number; // 最大角度（度）
  followSpeed: number; // 跟随速度
  returnSpeed: number; // 回归速度
  blinkFrequency: number; // 眨眼频率（秒）
  naturalMovement: boolean; // 自然移动
}

export interface EyeTrackingState {
  currentX: number;
  currentY: number;
  targetX: number;
  targetY: number;
  lastBlinkTime: number;
  isTracking: boolean;
  isNaturalMoving: boolean;
}

/**
 * 眼部追踪服务
 * 提供高级的眼部追踪、自然移动和眨眼效果
 */
export class EyeTrackingService {
  private model: PixiLive2DModel | null = null;
  private options: EyeTrackingOptions;
  private state: EyeTrackingState;
  private animationId: number | null = null;
  private naturalMovementTimer: NodeJS.Timeout | null = null;
  private blinkTimer: NodeJS.Timeout | null = null;

  // 参数名称映射
  private parameterNames = {
    eyeX: ['ParamEyeLookX', 'ParamEyeballX', 'PARAM_EYE_BALL_X'],
    eyeY: ['ParamEyeLookY', 'ParamEyeballY', 'PARAM_EYE_BALL_Y'],
    eyeOpenL: ['ParamEyeLOpen', 'PARAM_EYE_L_OPEN'],
    eyeOpenR: ['ParamEyeROpen', 'PARAM_EYE_R_OPEN'],
    browLY: ['ParamBrowLY', 'PARAM_BROW_L_Y'],
    browRY: ['ParamBrowRY', 'PARAM_BROW_R_Y']
  };

  constructor(options: Partial<EyeTrackingOptions> = {}) {
    this.options = {
      enabled: true,
      sensitivity: 0.8,
      smoothing: 0.7,
      maxAngle: 30,
      followSpeed: 0.15,
      returnSpeed: 0.05,
      blinkFrequency: 3, // 每3秒眨一次眼
      naturalMovement: true,
      ...options
    };

    this.state = {
      currentX: 0,
      currentY: 0,
      targetX: 0,
      targetY: 0,
      lastBlinkTime: 0,
      isTracking: false,
      isNaturalMoving: false
    };
  }

  /**
   * 设置Live2D模型
   */
  setModel(model: PixiLive2DModel): void {
    this.model = model;
    this.validateParameters();
    this.startTracking();
  }

  /**
   * 验证模型参数
   */
  private validateParameters(): void {
    if (!this.model) return;

    console.log('可用参数:', this.getAvailableParameters());
    
    // 检查眼部参数是否存在
    const eyeXParam = this.findParameter(this.parameterNames.eyeX);
    const eyeYParam = this.findParameter(this.parameterNames.eyeY);
    
    if (eyeXParam && eyeYParam) {
      console.log(`眼部追踪参数: ${eyeXParam}, ${eyeYParam}`);
    } else {
      console.warn('未找到眼部追踪参数');
    }
  }

  /**
   * 获取可用参数列表
   */
  private getAvailableParameters(): string[] {
    if (!this.model?.internalModel) return [];
    
    const parameters: string[] = [];
    const coreModel = this.model.internalModel.coreModel as any;
    
    if (coreModel && coreModel.getParameterCount) {
      const count = coreModel.getParameterCount();
      for (let i = 0; i < count; i++) {
        try {
          const paramId = coreModel.getParameterId(i);
          parameters.push(paramId);
        } catch (error) {
          // 忽略获取失败的参数
        }
      }
    }
    
    return parameters;
  }

  /**
   * 查找参数
   */
  private findParameter(candidates: string[]): string | null {
    if (!this.model?.internalModel) return null;
    
    for (const candidate of candidates) {
      try {
        // 尝试获取参数值来验证参数是否存在
        const coreModel = this.model.internalModel.coreModel as any;
        const value = coreModel?.getParameterValueById?.(candidate);
        if (value !== undefined) {
          return candidate;
        }
      } catch (error) {
        // 参数不存在，继续尝试下一个
      }
    }
    
    return null;
  }

  /**
   * 设置参数值
   */
  private setParameter(candidates: string[], value: number): void {
    const paramId = this.findParameter(candidates);
    if (paramId && this.model?.internalModel) {
      try {
        const coreModel = this.model.internalModel.coreModel as any;
        coreModel?.setParameterValueById?.(paramId, value);
      } catch (error) {
        console.warn(`设置参数失败: ${paramId}`, error);
      }
    }
  }

  /**
   * 开始追踪
   */
  startTracking(): void {
    if (!this.options.enabled || this.state.isTracking) return;

    this.state.isTracking = true;
    this.startAnimation();
    this.startNaturalMovement();
    this.startBlinking();
    
    console.log('眼部追踪已启动');
  }

  /**
   * 停止追踪
   */
  stopTracking(): void {
    this.state.isTracking = false;
    
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
    
    if (this.naturalMovementTimer) {
      clearTimeout(this.naturalMovementTimer);
      this.naturalMovementTimer = null;
    }
    
    if (this.blinkTimer) {
      clearTimeout(this.blinkTimer);
      this.blinkTimer = null;
    }
    
    console.log('眼部追踪已停止');
  }

  /**
   * 更新鼠标位置
   */
  updateMousePosition(mouseX: number, mouseY: number, containerWidth: number, containerHeight: number): void {
    if (!this.state.isTracking || !this.model) return;

    // 将鼠标坐标转换为模型坐标系
    const normalizedX = (mouseX / containerWidth - 0.5) * 2; // -1 到 1
    const normalizedY = (mouseY / containerHeight - 0.5) * 2; // -1 到 1

    // 应用灵敏度和角度限制
    const maxAngleRad = (this.options.maxAngle * Math.PI) / 180;
    const sensitivity = this.options.sensitivity;

    this.state.targetX = Math.max(-1, Math.min(1, normalizedX * sensitivity));
    this.state.targetY = Math.max(-1, Math.min(1, normalizedY * sensitivity));

    // 停止自然移动
    this.state.isNaturalMoving = false;
    if (this.naturalMovementTimer) {
      clearTimeout(this.naturalMovementTimer);
      this.naturalMovementTimer = null;
    }

    // 2秒后开始自然移动
    if (this.options.naturalMovement) {
      this.naturalMovementTimer = setTimeout(() => {
        this.startNaturalMovement();
      }, 2000);
    }
  }

  /**
   * 启动动画循环
   */
  private startAnimation(): void {
    const animate = () => {
      if (!this.state.isTracking) return;

      this.updateEyePosition();
      this.animationId = requestAnimationFrame(animate);
    };

    animate();
  }

  /**
   * 更新眼部位置
   */
  private updateEyePosition(): void {
    if (!this.model) return;

    const smoothing = this.options.smoothing;
    const speed = this.state.isNaturalMoving ? this.options.returnSpeed : this.options.followSpeed;

    // 平滑过渡到目标位置
    this.state.currentX += (this.state.targetX - this.state.currentX) * speed;
    this.state.currentY += (this.state.targetY - this.state.currentY) * speed;

    // 设置眼部参数
    this.setParameter(this.parameterNames.eyeX, this.state.currentX);
    this.setParameter(this.parameterNames.eyeY, this.state.currentY);

    // 根据眼部移动调整眉毛位置（可选）
    const browOffset = Math.abs(this.state.currentY) * 0.3;
    this.setParameter(this.parameterNames.browLY, browOffset);
    this.setParameter(this.parameterNames.browRY, browOffset);
  }

  /**
   * 启动自然移动
   */
  private startNaturalMovement(): void {
    if (!this.options.naturalMovement) return;

    this.state.isNaturalMoving = true;

    const createRandomMovement = () => {
      if (!this.state.isTracking || !this.state.isNaturalMoving) return;

      // 生成随机目标位置
      this.state.targetX = (Math.random() - 0.5) * 0.4; // 较小的随机移动
      this.state.targetY = (Math.random() - 0.5) * 0.3;

      // 1-3秒后进行下一次移动
      this.naturalMovementTimer = setTimeout(createRandomMovement, 1000 + Math.random() * 2000);
    };

    createRandomMovement();
  }

  /**
   * 启动眨眼
   */
  private startBlinking(): void {
    const blink = () => {
      if (!this.state.isTracking) return;

      this.performBlink();

      // 设置下次眨眼时间（添加随机性）
      const interval = this.options.blinkFrequency * 1000;
      const randomOffset = (Math.random() - 0.5) * interval * 0.5;
      
      this.blinkTimer = setTimeout(blink, interval + randomOffset);
    };

    // 首次眨眼延迟1秒
    this.blinkTimer = setTimeout(blink, 1000);
  }

  /**
   * 执行眨眼动作
   */
  private performBlink(): void {
    if (!this.model) return;

    const blinkDuration = 150; // 眨眼持续时间（毫秒）
    const steps = 15; // 动画步数
    const stepDuration = blinkDuration / steps;

    let step = 0;

    const animateBlink = () => {
      if (step < steps / 2) {
        // 闭眼阶段
        const progress = (step / (steps / 2));
        const eyeOpen = 1 - progress;
        
        this.setParameter(this.parameterNames.eyeOpenL, eyeOpen);
        this.setParameter(this.parameterNames.eyeOpenR, eyeOpen);
      } else {
        // 睁眼阶段
        const progress = ((step - steps / 2) / (steps / 2));
        const eyeOpen = progress;
        
        this.setParameter(this.parameterNames.eyeOpenL, eyeOpen);
        this.setParameter(this.parameterNames.eyeOpenR, eyeOpen);
      }

      step++;
      
      if (step < steps) {
        setTimeout(animateBlink, stepDuration);
      } else {
        // 确保眼睛完全睁开
        this.setParameter(this.parameterNames.eyeOpenL, 1);
        this.setParameter(this.parameterNames.eyeOpenR, 1);
      }
    };

    animateBlink();
  }

  /**
   * 手动触发眨眼
   */
  triggerBlink(): void {
    this.performBlink();
  }

  /**
   * 设置注视点
   */
  lookAt(x: number, y: number): void {
    this.state.targetX = Math.max(-1, Math.min(1, x));
    this.state.targetY = Math.max(-1, Math.min(1, y));
    this.state.isNaturalMoving = false;
  }

  /**
   * 回到中心位置
   */
  lookCenter(): void {
    this.lookAt(0, 0);
  }

  /**
   * 更新选项
   */
  updateOptions(options: Partial<EyeTrackingOptions>): void {
    this.options = { ...this.options, ...options };
    
    if (!options.enabled && this.state.isTracking) {
      this.stopTracking();
    } else if (options.enabled && !this.state.isTracking) {
      this.startTracking();
    }
  }

  /**
   * 获取当前状态
   */
  getState(): EyeTrackingState {
    return { ...this.state };
  }

  /**
   * 获取当前选项
   */
  getOptions(): EyeTrackingOptions {
    return { ...this.options };
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.stopTracking();
    this.model = null;
  }
} 