// @ts-nocheck
import * as PIXI from 'pixi.js';
// 使用通用导入，但只加载 Cubism 4 模型
import { Live2DModel as PixiLive2DModel } from 'pixi-live2d-display';
import { checkLive2DEnvironment, detectModelFormat } from '../../lib/live2d-setup';
import type {
  Live2DModel,
  Live2DConfig,
  Live2DModelState,
  Live2DLoadOptions,
  Live2DDisplayOptions,
  Live2DValidationResult,
  Live2DModelFileStructure
} from '../../types/live2d';

export interface Live2DEventCallbacks {
  onModelLoad?: (model: PixiLive2DModel) => void;
  onModelError?: (error: Error) => void;
  onExpressionChange?: (expression: string) => void;
  onMotionStart?: (motion: string) => void;
  onMotionEnd?: () => void;
}

/**
 * 增强的Live2D模型管理服务
 * 支持多模型、表情、动画、交互等完整功能
 */
export class Live2DService {
  private static instance: Live2DService;
  private config: Live2DConfig | null = null;
  private loadedModels: Map<string, PixiLive2DModel> = new Map();
  private currentModel: PixiLive2DModel | null = null;
  private currentModelId: string | null = null;
  private app: PIXI.Application | null = null;
  private container: HTMLElement | null = null;
  private callbacks: Live2DEventCallbacks = {};
  
  // 状态管理
  private state: Live2DModelState = {
    isLoaded: false,
    isLoading: false,
    currentModelId: null,
    currentExpression: null,
    currentMotion: null,
    status: 'idle',
    error: null,
    lastInteraction: null
  };

  // 交互状态
  private mousePosition = { x: 0, y: 0 };
  private isInteracting = false;
  private lastInteractionTime = 0;
  private idleTimer: NodeJS.Timeout | null = null;

  private constructor() {}

  public static getInstance(): Live2DService {
    if (!Live2DService.instance) {
      Live2DService.instance = new Live2DService();
    }
    return Live2DService.instance;
  }

  /**
   * 初始化Live2D服务
   */
  async initialize(container: HTMLElement, callbacks?: Live2DEventCallbacks): Promise<void> {
    try {
      this.setState({ isLoading: true, error: null });
      this.container = container;
      this.callbacks = callbacks || {};

      // 检查 Live2D 环境
      const envCheck = checkLive2DEnvironment();
      if (!envCheck.isReady) {
        throw new Error(`Live2D 环境未就绪: ${JSON.stringify(envCheck.checks)}`);
      }

      // 加载模型配置
      await this.loadModelConfig();

      // 初始化PIXI应用
      await this.initializePixiApp(container);

      // 设置交互事件
      this.setupInteractionEvents();

      console.log('Live2D服务初始化成功');
      this.setState({ isLoading: false });

    } catch (error) {
      console.error('Live2D服务初始化失败:', error);
      this.setState({ isLoading: false, error: (error as Error).message });
      this.callbacks.onModelError?.(error as Error);
      throw error;
    }
  }

  /**
   * 加载模型配置文件
   */
  private async loadModelConfig(): Promise<void> {
    try {
      const response = await fetch('/static/live2d/config/models.json');
      if (!response.ok) {
        throw new Error(`配置文件加载失败: ${response.status}`);
      }
      this.config = await response.json();
    } catch (error) {
      console.error('模型配置加载失败:', error);
      throw error;
    }
  }

  /**
   * 初始化PIXI应用
   */
  private async initializePixiApp(container: HTMLElement): Promise<void> {
    try {
      // 使用 PIXI v8 的新 API
      this.app = new PIXI.Application();

      // 初始化应用
      await this.app.init({
        width: container.offsetWidth || 400,
        height: container.offsetHeight || 600,
        backgroundAlpha: 0,
        antialias: true,
        resolution: window.devicePixelRatio || 1,
        autoDensity: true,
      });

      // 添加canvas到容器
      if (this.app.canvas) {
        container.appendChild(this.app.canvas);
      }

      // 设置舞台交互
      this.app.stage.eventMode = 'static';
      this.app.stage.hitArea = this.app.screen;

      // 监听窗口大小变化
      window.addEventListener('resize', this.handleResize.bind(this));

      console.log('PIXI应用初始化成功');
    } catch (error) {
      console.error('PIXI应用初始化失败:', error);
      throw error;
    }
  }

  /**
   * 设置交互事件
   */
  private setupInteractionEvents(): void {
    if (!this.app) return;

    // 鼠标移动事件
    this.app.stage.on('pointermove', (event) => {
      this.mousePosition.x = event.global.x;
      this.mousePosition.y = event.global.y;
      this.updateMouseTracking();
    });

    // 点击事件
    this.app.stage.on('pointerdown', (event) => {
      this.handleModelInteraction(event.global.x, event.global.y);
    });
  }

  /**
   * 更新鼠标跟踪
   */
  private updateMouseTracking(): void {
    if (!this.currentModel) return;

    const { x, y } = this.mousePosition;
    const stage = this.app!.stage;

    // 将鼠标坐标转换为模型相对坐标
    const modelBounds = this.currentModel.getBounds();
    const normalizedX = (x - modelBounds.x) / modelBounds.width;
    const normalizedY = (y - modelBounds.y) / modelBounds.height;

    // 更新视线跟踪（如果模型支持）
    if (this.currentModel.internalModel?.focusController) {
      this.currentModel.focus(normalizedX, normalizedY);
    }
  }

  /**
   * 处理模型交互
   */
  private handleModelInteraction(x: number, y: number): void {
    if (!this.currentModel) return;

    this.isInteracting = true;
    this.lastInteractionTime = Date.now();

    // 检测点击的身体部位
    const hitArea = this.getHitArea(x, y);
    
    if (hitArea) {
      this.playInteractionMotion(hitArea);
    } else {
      // 随机播放一个交互动作
      this.playRandomInteractionMotion();
    }

    // 重置空闲计时器
    this.resetIdleTimer();
  }

  /**
   * 获取点击区域
   */
  private getHitArea(x: number, y: number): string | null {
    if (!this.currentModel) return null;

    // 检查是否点击了头部区域
    const modelBounds = this.currentModel.getBounds();
    const relativeX = (x - modelBounds.x) / modelBounds.width;
    const relativeY = (y - modelBounds.y) / modelBounds.height;

    if (relativeY < 0.3) {
      return 'head';
    } else if (relativeY > 0.7) {
      return 'body';
    }

    return 'body';
  }

  /**
   * 播放交互动作
   */
  private async playInteractionMotion(hitArea: string): Promise<void> {
    if (!this.currentModel) return;

    try {
      let motionGroup = 'tap_body';
      
      if (hitArea === 'head') {
        motionGroup = 'flick_head';
      }

      await this.playMotion(motionGroup, 0);
    } catch (error) {
      console.warn('交互动作播放失败:', error);
    }
  }

  /**
   * 播放随机交互动作
   */
  private async playRandomInteractionMotion(): Promise<void> {
    const motions = ['tap_body', 'flick_head', 'shake'];
    const randomMotion = motions[Math.floor(Math.random() * motions.length)];
    
    try {
      await this.playMotion(randomMotion, 0);
    } catch (error) {
      console.warn('随机交互动作播放失败:', error);
    }
  }

  /**
   * 重置空闲计时器
   */
  private resetIdleTimer(): void {
    if (this.idleTimer) {
      clearTimeout(this.idleTimer);
    }

    // 5秒后回到待机状态
    this.idleTimer = setTimeout(() => {
      this.playIdleMotion();
    }, 5000);
  }

  /**
   * 播放待机动作
   */
  private async playIdleMotion(): Promise<void> {
    if (!this.currentModel || this.isInteracting) return;

    try {
      await this.playMotion('idle', 0);
    } catch (error) {
      console.warn('待机动作播放失败:', error);
    }
  }

  /**
   * 处理窗口大小变化
   */
  private handleResize(): void {
    if (!this.app || !this.container) return;

    const { offsetWidth, offsetHeight } = this.container;
    this.app.renderer.resize(offsetWidth, offsetHeight);

    // 重新定位当前模型
    if (this.currentModel) {
      this.repositionModel();
    }
  }

  /**
   * 重新定位模型
   */
  private repositionModel(): void {
    if (!this.currentModel || !this.app) return;

    const { width, height } = this.app.screen;
    
    // 重新计算模型位置
    this.currentModel.position.set(width * 0.5, height * 0.9);
    
    // 根据窗口大小调整缩放
    const scale = Math.min(width / 800, height / 600) * 0.3;
    this.currentModel.scale.set(scale);
  }

  /**
   * 加载指定模型
   */
  async loadModel(modelId: string, options: Live2DLoadOptions = {}): Promise<PixiLive2DModel> {
    try {
      this.setState({ isLoading: true, error: null });

      // 检查缓存
      if (options.useCache !== false && this.loadedModels.has(modelId)) {
        const cachedModel = this.loadedModels.get(modelId)!;
        return cachedModel;
      }

      const modelConfig = this.getModelById(modelId);
      if (!modelConfig) {
        throw new Error(`模型不存在: ${modelId}`);
      }

      // 检测模型格式
      const modelFormat = detectModelFormat(modelConfig.modelPath);
      console.log(`检测到模型格式: ${modelFormat} (${modelConfig.modelPath})`);

      // 尝试多个加载源
      const loadSources = this.getModelLoadSources(modelConfig);
      let model: PixiLive2DModel | null = null;
      let lastError: Error | null = null;

      for (const source of loadSources) {
        try {
          console.log(`正在从 ${source} 加载模型 (格式: ${modelFormat})...`);
          model = await PixiLive2DModel.from(source, {
            autoUpdate: true,
            autoInteract: false, // 我们自己处理交互
          });
          console.log(`模型加载成功: ${modelId}`);
          break;
        } catch (error) {
          console.warn(`从 ${source} 加载失败:`, error);
          lastError = error as Error;
        }
      }

      if (!model) {
        throw lastError || new Error(`所有加载源都失败: ${modelId}`);
      }

      // 设置模型属性
      this.setupModelProperties(model);

      // 缓存模型
      if (options.useCache !== false) {
        this.loadedModels.set(modelId, model);
      }

      this.setState({ isLoading: false });
      this.callbacks.onModelLoad?.(model);

      return model;
    } catch (error) {
      console.error(`模型加载失败: ${modelId}`, error);
      this.setState({ isLoading: false, error: (error as Error).message });
      this.callbacks.onModelError?.(error as Error);
      throw error;
    }
  }

  /**
   * 获取模型加载源
   */
  private getModelLoadSources(modelConfig: Live2DModel): string[] {
    const sources = [modelConfig.modelPath];

    // 添加CDN源
    if (this.config?.cdnConfig) {
      const cdnPath = this.convertToCDNPath(modelConfig.modelPath);
      sources.push(cdnPath);

      // 添加备用CDN源
      this.config.cdnConfig.fallbackUrls?.forEach(fallbackUrl => {
        const fallbackPath = modelConfig.modelPath.replace('/static/live2d/', '');
        sources.push(`${fallbackUrl}/${fallbackPath}`);
      });
    }

    return sources;
  }

  /**
   * 转换为CDN路径
   */
  private convertToCDNPath(localPath: string): string {
    if (!this.config?.cdnConfig) return localPath;
    
    const relativePath = localPath.replace('/static/live2d/', '');
    return `${this.config.cdnConfig.baseUrl}/${relativePath}`;
  }

  /**
   * 设置模型属性
   */
  private setupModelProperties(model: PixiLive2DModel): void {
    // 设置默认缩放和位置
    model.anchor.set(0.5, 1);
    
    if (this.app) {
      const { width, height } = this.app.screen;
      const scale = Math.min(width / 800, height / 600) * 0.3;
      
      model.scale.set(scale);
      model.position.set(width * 0.5, height * 0.9);
    }

    // 设置交互
    model.eventMode = 'static';
    model.cursor = 'pointer';

    // 设置物理效果（如果支持）
    if (model.internalModel?.physics) {
      model.internalModel.physics.enabled = true;
    }
  }

  /**
   * 切换到指定模型
   */
  async switchToModel(modelId: string, options: Live2DLoadOptions = {}): Promise<void> {
    try {
      // 加载新模型
      const newModel = await this.loadModel(modelId, options);
      
      // 移除当前模型
      if (this.currentModel && this.app) {
        this.app.stage.removeChild(this.currentModel);
      }

      // 显示新模型
      if (this.app) {
        this.app.stage.addChild(newModel);
      }

      // 更新当前模型引用
      this.currentModel = newModel;
      this.currentModelId = modelId;
      
      // 更新状态
      const modelConfig = this.getModelById(modelId);
      this.setState({ 
        currentModelId: modelConfig?.id || null,
        currentExpression: null,
        currentMotion: null,
        status: 'idle',
        error: null,
        lastInteraction: null
      });

      // 开始待机动画
      this.resetIdleTimer();

      console.log(`已切换到模型: ${modelId}`);
    } catch (error) {
      console.error(`模型切换失败: ${modelId}`, error);
      throw error;
    }
  }

  /**
   * 播放表情
   */
  async playExpression(expressionId: string): Promise<void> {
    if (!this.currentModel) {
      throw new Error('没有加载的模型');
    }

    try {
      await this.currentModel.expression(expressionId);
      this.setState({ currentExpression: expressionId });
      this.callbacks.onExpressionChange?.(expressionId);
      
      console.log(`表情播放成功: ${expressionId}`);
    } catch (error) {
      console.error(`表情播放失败: ${expressionId}`, error);
      throw error;
    }
  }

  /**
   * 播放动作
   */
  async playMotion(motionGroup: string, motionIndex: number = 0): Promise<void> {
    if (!this.currentModel) {
      throw new Error('没有加载的模型');
    }

    try {
      this.setState({ isPlaying: true });
      this.callbacks.onMotionStart?.(motionGroup);

      await this.currentModel.motion(motionGroup, motionIndex);
      
      this.setState({ isPlaying: false });
      this.callbacks.onMotionEnd?.();
      
      console.log(`动作播放成功: ${motionGroup}[${motionIndex}]`);
    } catch (error) {
      this.setState({ isPlaying: false });
      console.error(`动作播放失败: ${motionGroup}[${motionIndex}]`, error);
      throw error;
    }
  }

  /**
   * 获取所有可用模型
   */
  getAvailableModels(): Live2DModel[] {
    return this.config?.models || [];
  }

  /**
   * 根据ID获取模型配置
   */
  getModelById(id: string): Live2DModel | null {
    return this.config?.models.find(model => model.id === id) || null;
  }

  /**
   * 获取当前模型的表情列表
   */
  getCurrentModelExpressions(): string[] {
    if (!this.currentModelId) return [];
    
    const modelConfig = this.getModelById(this.currentModelId);
    return modelConfig?.expressions.map(exp => exp.file.replace('.exp3.json', '')) || [];
  }

  /**
   * 获取当前模型的动作列表
   */
  getCurrentModelMotions(): { [group: string]: string[] } {
    if (!this.currentModelId) return {};
    
    const modelConfig = this.getModelById(this.currentModelId);
    if (!modelConfig) return {};

    const motions: { [group: string]: string[] } = {};
    modelConfig.motions.forEach(motion => {
      const group = motion.name.split('_')[0] || 'default';
      if (!motions[group]) {
        motions[group] = [];
      }
      motions[group].push(motion.file.replace('.motion3.json', ''));
    });

    return motions;
  }

  /**
   * 重置模型状态
   */
  resetModel(): void {
    if (!this.currentModel) return;

    try {
      // 重置表情
      if (this.currentModel.internalModel?.expressionManager) {
        this.currentModel.internalModel.expressionManager.resetExpression();
      }

      // 重置为待机状态
      this.setState({ currentExpression: null, isPlaying: false });
      this.playIdleMotion();
      
      console.log('模型状态已重置');
    } catch (error) {
      console.error('模型重置失败:', error);
    }
  }

  /**
   * 获取当前状态
   */
  getState(): Live2DModelState {
    return { ...this.state };
  }

  /**
   * 更新状态
   */
  private setState(updates: Partial<Live2DModelState>): void {
    this.state = { ...this.state, ...updates };
  }

  /**
   * 获取默认模型ID
   */
  getDefaultModelId(): string {
    return this.config?.defaultModel || 'idol';
  }

  /**
   * 清理资源
   */
  destroy(): void {
    // 清理计时器
    if (this.idleTimer) {
      clearTimeout(this.idleTimer);
      this.idleTimer = null;
    }

    // 清理事件监听器
    window.removeEventListener('resize', this.handleResize.bind(this));

    // 清理模型
    this.loadedModels.forEach(model => {
      if (model.destroy) {
        model.destroy();
      }
    });
    this.loadedModels.clear();

    // 清理PIXI应用
    if (this.app) {
      this.app.destroy(true);
      this.app = null;
    }

    // 重置状态
    this.currentModel = null;
    this.currentModelId = null;
    this.container = null;
    this.callbacks = {};
    
    console.log('Live2D服务已销毁');
  }
}

export default Live2DService;
