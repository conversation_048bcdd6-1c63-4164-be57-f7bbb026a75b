import { Live2DModel as PixiLive2DModel } from 'pixi-live2d-display';

export interface EmotionRule {
  keywords: string[];
  expressions: string[];
  priority: number;
  duration?: number; // 表情持续时间（毫秒）
}

export interface EmotionState {
  currentEmotion: string | null;
  intensity: number; // 0-1, 情感强度
  lastAnalysisTime: number;
  isProcessing: boolean;
  history: EmotionHistoryItem[];
}

export interface EmotionHistoryItem {
  emotion: string;
  text: string;
  timestamp: number;
  confidence: number;
}

export interface EmotionSystemOptions {
  enabled: boolean;
  sensitivity: number; // 0-1, 情感检测灵敏度
  autoRevert: boolean; // 是否自动回到默认表情
  revertDelay: number; // 自动回复延迟（毫秒）
  historyLimit: number; // 历史记录限制
  contextAnalysis: boolean; // 是否启用上下文分析
}

/**
 * 情感表达系统
 * 分析文本情感并自动切换相应的Live2D表情
 */
export class EmotionSystem {
  private model: PixiLive2DModel | null = null;
  private options: EmotionSystemOptions;
  private state: EmotionState;
  private emotionRules: Map<string, EmotionRule>;
  private revertTimer: NodeJS.Timeout | null = null;
  private playExpressionCallback?: (expression: string) => Promise<void>;

  constructor(options: Partial<EmotionSystemOptions> = {}) {
    this.options = {
      enabled: true,
      sensitivity: 0.7,
      autoRevert: true,
      revertDelay: 3000,
      historyLimit: 50,
      contextAnalysis: true,
      ...options
    };

    this.state = {
      currentEmotion: null,
      intensity: 0,
      lastAnalysisTime: 0,
      isProcessing: false,
      history: []
    };

    this.emotionRules = new Map();
    this.initializeEmotionRules();
  }

  /**
   * 初始化情感规则
   */
  private initializeEmotionRules(): void {
    const rules: Record<string, EmotionRule> = {
      // 高兴/开心
      happy: {
        keywords: [
          '高兴', '开心', '快乐', '喜悦', '兴奋', '愉快', '满意', '欣喜',
          '哈哈', '嘻嘻', '棒', '太好了', '真棒', '厉害', '成功', '胜利',
          '😊', '😄', '😃', '🎉', '👏', '✨'
        ],
        expressions: ['f02', 'happy', 'smile', 'joy'],
        priority: 8,
        duration: 2500
      },

      // 伤心/难过
      sad: {
        keywords: [
          '伤心', '难过', '痛苦', '失望', '沮丧', '悲伤', '哭', '泪',
          '不开心', '郁闷', '失落', '绝望', '心痛', '想哭',
          '😢', '😭', '😔', '💔', '😞'
        ],
        expressions: ['f04', 'sad', 'cry', 'disappointed'],
        priority: 9,
        duration: 3000
      },

      // 生气/愤怒
      angry: {
        keywords: [
          '生气', '愤怒', '气愤', '恼火', '愤慨', '火大', '暴怒',
          '讨厌', '烦', '可恶', '混蛋', '该死', '气死了',
          '😠', '😡', '🤬', '💢', '😤'
        ],
        expressions: ['f03', 'angry', 'mad', 'furious'],
        priority: 10,
        duration: 2000
      },

      // 惊讶/震惊
      surprised: {
        keywords: [
          '惊讶', '震惊', '吃惊', '意外', '不敢相信', '天哪', '哇',
          '真的吗', '怎么可能', '没想到', '居然', '竟然',
          '😲', '😱', '🤯', '😮', '😯'
        ],
        expressions: ['f04', 'surprised', 'shocked', 'amazed'],
        priority: 7,
        duration: 2000
      },

      // 害羞/羞涩
      shy: {
        keywords: [
          '害羞', '羞涩', '不好意思', '脸红', '羞', '羞耻',
          '谢谢', '不敢当', '过奖了', '哪里哪里',
          '😳', '😊', '🙈', '☺️'
        ],
        expressions: ['f06', 'shy', 'blush', 'embarrassed'],
        priority: 6,
        duration: 2500
      },

      // 困惑/疑惑
      confused: {
        keywords: [
          '困惑', '疑惑', '不明白', '搞不懂', '什么意思', '怎么回事',
          '奇怪', '为什么', '怎么', '啊？', '嗯？', '什么？',
          '🤔', '😕', '😵', '🤷'
        ],
        expressions: ['f05', 'confused', 'puzzled', 'thinking'],
        priority: 5,
        duration: 2000
      },

      // 无聊/厌倦
      bored: {
        keywords: [
          '无聊', '厌倦', '没意思', '枯燥', '乏味', '没劲',
          '好无聊', '真无聊', '没事干', '闲着',
          '😑', '😐', '🙄', '😴'
        ],
        expressions: ['f07', 'bored', 'tired', 'sleepy'],
        priority: 4,
        duration: 3000
      },

      // 默认/中性
      neutral: {
        keywords: [
          '你好', '再见', '谢谢', '请', '好的', '是的', '不是',
          '可以', '不可以', '知道了', '明白了'
        ],
        expressions: ['f01', 'default', 'neutral', 'normal'],
        priority: 1,
        duration: 1000
      }
    };

    // 将规则添加到Map中
    Object.entries(rules).forEach(([emotion, rule]) => {
      this.emotionRules.set(emotion, rule);
    });
  }

  /**
   * 设置Live2D模型和表情播放回调
   */
  setModel(model: PixiLive2DModel, playExpressionCallback: (expression: string) => Promise<void>): void {
    this.model = model;
    this.playExpressionCallback = playExpressionCallback;
  }

  /**
   * 分析文本情感
   */
  async analyzeText(text: string): Promise<string | null> {
    if (!this.options.enabled || !text.trim()) return null;

    this.state.isProcessing = true;
    this.state.lastAnalysisTime = Date.now();

    try {
      // 预处理文本
      const processedText = this.preprocessText(text);
      
      // 情感检测
      const emotionResults = this.detectEmotions(processedText);
      
      // 上下文分析
      let finalEmotion = this.selectBestEmotion(emotionResults);
      
      if (this.options.contextAnalysis) {
        finalEmotion = this.applyContextAnalysis(finalEmotion, processedText);
      }

      // 记录历史
      if (finalEmotion) {
        this.recordEmotionHistory(finalEmotion, text, emotionResults.get(finalEmotion) || 0);
      }

      return finalEmotion;
    } finally {
      this.state.isProcessing = false;
    }
  }

  /**
   * 预处理文本
   */
  private preprocessText(text: string): string {
    return text
      .toLowerCase()
      .replace(/[，。！？；：""''（）【】《》]/g, ' ') // 移除标点符号
      .replace(/\s+/g, ' ') // 合并空格
      .trim();
  }

  /**
   * 检测情感
   */
  private detectEmotions(text: string): Map<string, number> {
    const results = new Map<string, number>();

    this.emotionRules.forEach((rule, emotion) => {
      let score = 0;
      let matchCount = 0;

      rule.keywords.forEach(keyword => {
        const regex = new RegExp(keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
        const matches = text.match(regex);
        if (matches) {
          matchCount += matches.length;
          score += matches.length * rule.priority;
        }
      });

      if (matchCount > 0) {
        // 应用灵敏度调整
        score *= this.options.sensitivity;
        
        // 考虑文本长度
        const textLengthFactor = Math.min(1, text.length / 50);
        score *= textLengthFactor;
        
        results.set(emotion, score);
      }
    });

    return results;
  }

  /**
   * 选择最佳情感
   */
  private selectBestEmotion(emotionResults: Map<string, number>): string | null {
    if (emotionResults.size === 0) return null;

    let bestEmotion = null;
    let bestScore = 0;

    emotionResults.forEach((score, emotion) => {
      if (score > bestScore) {
        bestScore = score;
        bestEmotion = emotion;
      }
    });

    // 应用最小阈值
    const minThreshold = 2;
    return bestScore >= minThreshold ? bestEmotion : null;
  }

  /**
   * 应用上下文分析
   */
  private applyContextAnalysis(emotion: string | null, text: string): string | null {
    if (!emotion || this.state.history.length === 0) return emotion;

    // 获取最近的情感历史
    const recentHistory = this.state.history.slice(-3);
    
    // 检查情感连贯性
    const recentEmotions = recentHistory.map(item => item.emotion);
    const emotionFrequency = new Map<string, number>();
    
    recentEmotions.forEach(e => {
      emotionFrequency.set(e, (emotionFrequency.get(e) || 0) + 1);
    });

    // 如果当前情感与最近情感差异太大，降低置信度
    const lastEmotion = recentHistory[recentHistory.length - 1]?.emotion;
    if (lastEmotion && this.areEmotionsConflicting(emotion, lastEmotion)) {
      // 需要更强的证据来改变情感
      const rule = this.emotionRules.get(emotion);
      if (rule && rule.priority < 8) {
        return lastEmotion; // 保持之前的情感
      }
    }

    return emotion;
  }

  /**
   * 检查情感是否冲突
   */
  private areEmotionsConflicting(emotion1: string, emotion2: string): boolean {
    const conflictPairs = [
      ['happy', 'sad'],
      ['happy', 'angry'],
      ['sad', 'happy'],
      ['angry', 'shy'],
      ['surprised', 'bored']
    ];

    return conflictPairs.some(pair => 
      (pair[0] === emotion1 && pair[1] === emotion2) ||
      (pair[0] === emotion2 && pair[1] === emotion1)
    );
  }

  /**
   * 记录情感历史
   */
  private recordEmotionHistory(emotion: string, text: string, confidence: number): void {
    const historyItem: EmotionHistoryItem = {
      emotion,
      text,
      timestamp: Date.now(),
      confidence
    };

    this.state.history.push(historyItem);

    // 限制历史记录数量
    if (this.state.history.length > this.options.historyLimit) {
      this.state.history.shift();
    }
  }

  /**
   * 播放情感表情
   */
  async playEmotion(emotion: string): Promise<void> {
    if (!this.playExpressionCallback || !this.model) return;

    const rule = this.emotionRules.get(emotion);
    if (!rule) return;

    // 选择合适的表情
    const expression = this.selectExpressionForEmotion(rule);
    if (!expression) return;

    try {
      await this.playExpressionCallback(expression);
      
      this.state.currentEmotion = emotion;
      this.state.intensity = 1.0;

      // 设置自动回复计时器
      if (this.options.autoRevert) {
        this.setRevertTimer(rule.duration || this.options.revertDelay);
      }

      console.log(`播放情感表情: ${emotion} -> ${expression}`);
    } catch (error) {
      console.error('播放情感表情失败:', error);
    }
  }

  /**
   * 为情感选择表情
   */
  private selectExpressionForEmotion(rule: EmotionRule): string | null {
    // 从可用表情中选择一个
    return rule.expressions[0] || null;
  }

  /**
   * 设置自动回复计时器
   */
  private setRevertTimer(delay: number): void {
    if (this.revertTimer) {
      clearTimeout(this.revertTimer);
    }

    this.revertTimer = setTimeout(() => {
      this.revertToNeutral();
    }, delay);
  }

  /**
   * 回复到中性表情
   */
  private async revertToNeutral(): Promise<void> {
    if (this.state.currentEmotion === 'neutral') return;

    try {
      await this.playEmotion('neutral');
    } catch (error) {
      console.error('回复到中性表情失败:', error);
    }
  }

  /**
   * 分析并播放情感
   */
  async analyzeAndPlay(text: string): Promise<void> {
    const emotion = await this.analyzeText(text);
    if (emotion) {
      await this.playEmotion(emotion);
    }
  }

  /**
   * 添加自定义情感规则
   */
  addEmotionRule(emotion: string, rule: EmotionRule): void {
    this.emotionRules.set(emotion, rule);
  }

  /**
   * 移除情感规则
   */
  removeEmotionRule(emotion: string): void {
    this.emotionRules.delete(emotion);
  }

  /**
   * 获取情感历史
   */
  getEmotionHistory(): EmotionHistoryItem[] {
    return [...this.state.history];
  }

  /**
   * 清除情感历史
   */
  clearEmotionHistory(): void {
    this.state.history = [];
  }

  /**
   * 获取当前情感统计
   */
  getEmotionStatistics(): Map<string, number> {
    const stats = new Map<string, number>();
    
    this.state.history.forEach(item => {
      stats.set(item.emotion, (stats.get(item.emotion) || 0) + 1);
    });

    return stats;
  }

  /**
   * 更新选项
   */
  updateOptions(options: Partial<EmotionSystemOptions>): void {
    this.options = { ...this.options, ...options };
  }

  /**
   * 获取当前状态
   */
  getState(): EmotionState {
    return { ...this.state };
  }

  /**
   * 获取当前选项
   */
  getOptions(): EmotionSystemOptions {
    return { ...this.options };
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    if (this.revertTimer) {
      clearTimeout(this.revertTimer);
      this.revertTimer = null;
    }

    this.model = null;
    this.playExpressionCallback = undefined;
    this.state.history = [];
  }
} 