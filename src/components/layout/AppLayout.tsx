import React from 'react';
import { cn } from '@/lib/utils';
import { MainPanel } from './MainPanel';
import { InfoPanel } from './InfoPanel';
import { GridBackground } from './GridBackground';
import { Live2DContainer } from '@/components/features/live2d/Live2DContainer';

interface AppLayoutProps {
  className?: string;
}

export function AppLayout({ className }: AppLayoutProps) {
  return (
    <div className={cn(
      "min-h-screen bg-background text-foreground relative overflow-hidden",
      "flex flex-col lg:flex-row",
      className
    )}>
      {/* 网格背景 */}
      <GridBackground />
      
      {/* 主内容区域 */}
      <div className="relative z-10 flex flex-col lg:flex-row w-full h-screen">
        {/* 左侧主面板 */}
        <div className="flex-1 p-4 lg:p-8">
          <MainPanel />
        </div>
        
        {/* 右侧信息面板 */}
        <div className="w-full lg:w-96 xl:w-[400px] p-4 lg:p-8 lg:pl-0">
          <InfoPanel />
        </div>
      </div>

      {/* 固定位置的Live2D容器 */}
      <Live2DContainer 
        width={400}
        height={600}
        position="fixed"
        bottom={0}
        right={20}
        zIndex={30}
        autoLoad={true}
        enableInteraction={true}
      />

      {/* 科技感装饰线 */}
      <div className="fixed top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary/50 to-transparent z-50" />
      <div className="fixed bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary/50 to-transparent z-50" />
      <div className="fixed top-0 left-0 w-px h-full bg-gradient-to-b from-transparent via-primary/50 to-transparent z-50" />
      <div className="fixed top-0 right-0 w-px h-full bg-gradient-to-b from-transparent via-primary/50 to-transparent z-50" />

      {/* 角落发光效果 */}
      <div className="fixed top-0 left-0 w-32 h-32 bg-primary/10 rounded-full blur-3xl -translate-x-16 -translate-y-16 z-0" />
      <div className="fixed top-0 right-0 w-32 h-32 bg-secondary/10 rounded-full blur-3xl translate-x-16 -translate-y-16 z-0" />
      <div className="fixed bottom-0 left-0 w-32 h-32 bg-secondary/10 rounded-full blur-3xl -translate-x-16 translate-y-16 z-0" />
      <div className="fixed bottom-0 right-0 w-32 h-32 bg-primary/10 rounded-full blur-3xl translate-x-16 translate-y-16 z-0" />
    </div>
  );
}