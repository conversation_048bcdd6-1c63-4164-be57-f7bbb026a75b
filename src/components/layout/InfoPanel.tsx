import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Live2DPanel } from '@/components/features/live2d/Live2DPanel';
import { useLive2D } from '@/hooks/useLive2D';
import { cn } from '@/lib/utils';
import { 
  User, 
  Database, 
  FileText, 
  Settings, 
  Activity,
  Cpu,
  HardDrive,
  Wifi
} from 'lucide-react';

interface InfoPanelProps {
  className?: string;
}

export function InfoPanel({ className }: InfoPanelProps) {
  // 创建Live2D Hook实例供Live2DPanel使用
  const live2d = useLive2D({
    autoLoadDefault: false, // 在面板中手动控制加载
    enableInteraction: true,
    enableMouseTracking: true
  });

  return (
    <div className={cn(
      "h-full flex flex-col space-y-6",
      className
    )}>
      {/* Live2D 虚拟角色面板 */}
      <Live2DPanel 
        width={280}
        height={350}
        live2d={live2d}
      />

      {/* 音频缓存面板 */}
      <Card className="border-orange-500/20 bg-background/95 backdrop-blur-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-orange-400 font-mono text-sm">
              <Database className="w-4 h-4 inline mr-2" />
              Audio Cache
            </CardTitle>
            <Badge variant="outline" className="text-xs">
              0/10
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="text-center text-muted-foreground py-8">
              <Database className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <div className="text-sm font-mono">No cached audio</div>
              <div className="text-xs opacity-60 mt-1">Audio files will appear here</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 系统日志面板 */}
      <Card className="border-blue-500/20 bg-background/95 backdrop-blur-sm flex-1">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-blue-400 font-mono text-sm">
              <FileText className="w-4 h-4 inline mr-2" />
              System Logs
            </CardTitle>
            <div className="flex gap-1">
              <Badge variant="outline" className="text-xs">
                <Activity className="w-3 h-3 mr-1" />
                Live
              </Badge>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="p-0">
          <ScrollArea className="h-48 px-4">
            <div className="space-y-2 py-2">
              {/* 示例日志条目 */}
              <div className="text-xs font-mono space-y-1">
                <div className="flex items-center gap-2 text-green-400">
                  <span className="text-muted-foreground">12:34:56</span>
                  <span>[INFO]</span>
                  <span>Live2D service initialized</span>
                </div>
                <div className="flex items-center gap-2 text-blue-400">
                  <span className="text-muted-foreground">12:34:57</span>
                  <span>[INFO]</span>
                  <span>Model configuration loaded</span>
                </div>
                <div className="flex items-center gap-2 text-orange-400">
                  <span className="text-muted-foreground">12:34:58</span>
                  <span>[WARN]</span>
                  <span>No default model specified</span>
                </div>
              </div>
            </div>
          </ScrollArea>
          
          <div className="p-3 border-t border-border/50 bg-muted/20">
            <div className="text-xs text-muted-foreground text-center font-mono">
              System ready • Live2D loaded • WebSocket pending
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 系统状态面板 */}
      <Card className="border-purple-500/20 bg-background/95 backdrop-blur-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-purple-400 font-mono text-sm">
            <Settings className="w-4 h-4 inline mr-2" />
            System Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {/* 连接状态 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Wifi className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm">WebSocket</span>
            </div>
            <Badge variant="outline" className="text-xs">
              Disconnected
            </Badge>
          </div>

          {/* Live2D状态 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <User className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm">Live2D</span>
            </div>
            <Badge 
              variant={live2d.state.currentModel ? "default" : "outline"} 
              className="text-xs"
            >
              {live2d.state.currentModel ? "Loaded" : "Standby"}
            </Badge>
          </div>

          {/* 音频缓存状态 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <HardDrive className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm">Cache</span>
            </div>
            <Badge variant="outline" className="text-xs">
              0 MB / 50 MB
            </Badge>
          </div>

          {/* 系统性能 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Cpu className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm">Performance</span>
            </div>
            <Badge variant="terminal" className="text-xs">
              Optimal
            </Badge>
          </div>

          {/* 快捷键提示 */}
          <div className="pt-2 border-t border-border/30">
            <div className="text-xs text-muted-foreground space-y-1">
              <div><kbd className="px-1 py-0.5 bg-muted rounded text-xs">Space</kbd> Random Expression</div>
              <div><kbd className="px-1 py-0.5 bg-muted rounded text-xs">Enter</kbd> Random Motion</div>
              <div><kbd className="px-1 py-0.5 bg-muted rounded text-xs">R</kbd> Reset Model</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}