import React from 'react';
import { cn } from '@/lib/utils';

interface GridBackgroundProps {
  className?: string;
  variant?: 'default' | 'cyber' | 'terminal';
}

export function GridBackground({ className, variant = 'default' }: GridBackgroundProps) {
  const getVariantClasses = () => {
    switch (variant) {
      case 'cyber':
        return 'bg-cyber-grid';
      case 'terminal':
        return 'bg-grid-pattern';
      default:
        return 'bg-grid-pattern';
    }
  };

  return (
    <div className={cn(
      "fixed inset-0 z-0 pointer-events-none",
      "transition-opacity duration-1000",
      getVariantClasses(),
      className
    )}>
      {/* 网格背景 */}
      <div 
        className="absolute inset-0 opacity-20"
        style={{
          backgroundSize: '60px 60px',
          backgroundPosition: '0 0, 30px 30px',
        }}
      />
      
      {/* 动态扫描线效果 */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-cyber-blue/30 to-transparent animate-scan-line" />
      </div>
      
      {/* 角落发光效果 */}
      <div className="absolute top-0 left-0 w-32 h-32 bg-gradient-radial from-cyber-blue/10 to-transparent rounded-full blur-xl" />
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-radial from-cyber-green/10 to-transparent rounded-full blur-xl" />
      <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-radial from-cyber-purple/10 to-transparent rounded-full blur-xl" />
      <div className="absolute bottom-0 right-0 w-32 h-32 bg-gradient-radial from-cyber-orange/10 to-transparent rounded-full blur-xl" />
      
      {/* 中央聚焦区域 */}
      <div className="absolute inset-0 bg-gradient-radial from-transparent via-transparent to-background/20" />
    </div>
  );
}