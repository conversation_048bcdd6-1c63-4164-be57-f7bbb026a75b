import React, { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';
import { MessageCircle, X, Volume2, VolumeX } from 'lucide-react';

export interface DialogueBubbleProps {
  text: string;
  visible: boolean;
  position?: 'top' | 'bottom' | 'left' | 'right';
  variant?: 'default' | 'thought' | 'shout' | 'whisper';
  autoHide?: boolean;
  hideDelay?: number;
  onHide?: () => void;
  className?: string;
  showSpeaker?: boolean;
  speakerName?: string;
  animated?: boolean;
  typewriterEffect?: boolean;
  typewriterSpeed?: number;
}

interface BubbleStyle {
  container: string;
  bubble: string;
  tail: string;
  text: string;
}

export function DialogueBubble({
  text,
  visible,
  position = 'top',
  variant = 'default',
  autoHide = true,
  hideDelay = 3000,
  onHide,
  className,
  showSpeaker = false,
  speakerName = '角色',
  animated = true,
  typewriterEffect = false,
  typewriterSpeed = 50
}: DialogueBubbleProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [displayText, setDisplayText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const hideTimerRef = useRef<NodeJS.Timeout | null>(null);
  const typewriterTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 处理显示/隐藏逻辑
  useEffect(() => {
    if (visible) {
      setIsVisible(true);
      
      if (typewriterEffect) {
        startTypewriterEffect();
      } else {
        setDisplayText(text);
      }

      // 设置自动隐藏
      if (autoHide && hideDelay > 0) {
        hideTimerRef.current = setTimeout(() => {
          handleHide();
        }, hideDelay);
      }
    } else {
      handleHide();
    }

    return () => {
      if (hideTimerRef.current) {
        clearTimeout(hideTimerRef.current);
        hideTimerRef.current = null;
      }
      if (typewriterTimerRef.current) {
        clearTimeout(typewriterTimerRef.current);
        typewriterTimerRef.current = null;
      }
    };
  }, [visible, text, autoHide, hideDelay]);

  // 打字机效果
  const startTypewriterEffect = () => {
    setDisplayText('');
    setIsTyping(true);
    let currentIndex = 0;

    const typeNextChar = () => {
      if (currentIndex < text.length) {
        setDisplayText(text.slice(0, currentIndex + 1));
        currentIndex++;
        typewriterTimerRef.current = setTimeout(typeNextChar, typewriterSpeed);
      } else {
        setIsTyping(false);
      }
    };

    typeNextChar();
  };

  const handleHide = () => {
    setIsVisible(false);
    setIsTyping(false);
    if (typewriterTimerRef.current) {
      clearTimeout(typewriterTimerRef.current);
      typewriterTimerRef.current = null;
    }
    onHide?.();
  };

  const handleManualHide = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (hideTimerRef.current) {
      clearTimeout(hideTimerRef.current);
      hideTimerRef.current = null;
    }
    handleHide();
  };

  // 获取样式配置
  const getVariantStyles = (): BubbleStyle => {
    const baseStyles = {
      container: 'absolute z-50 max-w-xs',
      bubble: 'relative px-4 py-3 rounded-2xl shadow-xl backdrop-blur-sm border',
      tail: 'absolute w-4 h-4 transform rotate-45',
      text: 'text-sm leading-relaxed'
    };

    switch (variant) {
      case 'thought':
        return {
          ...baseStyles,
          bubble: cn(baseStyles.bubble, 
            'bg-purple-500/20 border-purple-400/30 text-purple-100',
            'before:content-["💭"] before:absolute before:-top-2 before:-left-2 before:text-lg'
          ),
          tail: cn(baseStyles.tail, 'bg-purple-500/20 border-purple-400/30')
        };
      
      case 'shout':
        return {
          ...baseStyles,
          bubble: cn(baseStyles.bubble,
            'bg-red-500/20 border-red-400/30 text-red-100 animate-pulse',
            'before:content-["💢"] before:absolute before:-top-2 before:-right-2 before:text-lg'
          ),
          tail: cn(baseStyles.tail, 'bg-red-500/20 border-red-400/30'),
          text: cn(baseStyles.text, 'font-bold')
        };
      
      case 'whisper':
        return {
          ...baseStyles,
          bubble: cn(baseStyles.bubble,
            'bg-gray-700/40 border-gray-500/30 text-gray-200',
            'before:content-["🤫"] before:absolute before:-top-2 before:-left-2 before:text-lg'
          ),
          tail: cn(baseStyles.tail, 'bg-gray-700/40 border-gray-500/30'),
          text: cn(baseStyles.text, 'italic text-xs')
        };
      
      default:
        return {
          ...baseStyles,
          bubble: cn(baseStyles.bubble, 'bg-background/95 border-primary/30 text-foreground'),
          tail: cn(baseStyles.tail, 'bg-background/95 border-primary/30')
        };
    }
  };

  const getPositionStyles = () => {
    switch (position) {
      case 'top':
        return {
          container: 'bottom-full left-1/2 transform -translate-x-1/2 mb-4',
          tail: 'top-full left-1/2 transform -translate-x-1/2 -mt-2'
        };
      case 'bottom':
        return {
          container: 'top-full left-1/2 transform -translate-x-1/2 mt-4',
          tail: 'bottom-full left-1/2 transform -translate-x-1/2 -mb-2 rotate-45'
        };
      case 'left':
        return {
          container: 'right-full top-1/2 transform -translate-y-1/2 mr-4',
          tail: 'left-full top-1/2 transform -translate-y-1/2 -ml-2 rotate-45'
        };
      case 'right':
        return {
          container: 'left-full top-1/2 transform -translate-y-1/2 ml-4',
          tail: 'right-full top-1/2 transform -translate-y-1/2 -mr-2 rotate-45'
        };
      default:
        return {
          container: 'bottom-full left-1/2 transform -translate-x-1/2 mb-4',
          tail: 'top-full left-1/2 transform -translate-x-1/2 -mt-2'
        };
    }
  };

  const styles = getVariantStyles();
  const positionStyles = getPositionStyles();

  if (!isVisible) return null;

  return (
    <div 
      className={cn(
        styles.container,
        positionStyles.container,
        animated && 'animate-in fade-in slide-in-from-bottom-2 duration-300',
        !isVisible && animated && 'animate-out fade-out slide-out-to-bottom-2 duration-200',
        className
      )}
    >
      {/* 对话气泡 */}
      <div className={styles.bubble}>
        {/* 说话者名称 */}
        {showSpeaker && (
          <div className="flex items-center gap-2 mb-2 pb-2 border-b border-current/20">
            <MessageCircle className="w-3 h-3" />
            <span className="text-xs font-semibold opacity-80">{speakerName}</span>
            {isTyping && <Volume2 className="w-3 h-3 animate-pulse" />}
            {!isTyping && <VolumeX className="w-3 h-3 opacity-60" />}
          </div>
        )}
        
        {/* 对话内容 */}
        <div className={styles.text}>
          {displayText}
          {isTyping && (
            <span className="inline-block w-1 h-4 bg-current ml-1 animate-pulse" />
          )}
        </div>
        
        {/* 关闭按钮 */}
        <button
          onClick={handleManualHide}
          className="absolute -top-2 -right-2 w-6 h-6 rounded-full bg-destructive/80 hover:bg-destructive text-destructive-foreground flex items-center justify-center transition-colors"
          aria-label="关闭对话"
        >
          <X className="w-3 h-3" />
        </button>
      </div>
      
      {/* 气泡尾巴 */}
      <div className={cn(styles.tail, positionStyles.tail)} />
      
      {/* 装饰效果 */}
      {variant === 'default' && (
        <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-primary/5 to-secondary/5 pointer-events-none" />
      )}
    </div>
  );
}

// 对话气泡管理器Hook
export function useDialogueBubble() {
  const [bubbles, setBubbles] = useState<Array<{
    id: string;
    text: string;
    visible: boolean;
    variant?: DialogueBubbleProps['variant'];
    speakerName?: string;
  }>>([]);

  const showBubble = (
    text: string, 
    options: {
      variant?: DialogueBubbleProps['variant'];
      speakerName?: string;
      duration?: number;
    } = {}
  ) => {
    const id = Date.now().toString();
    const newBubble = {
      id,
      text,
      visible: true,
      variant: options.variant || 'default',
      speakerName: options.speakerName
    };

    setBubbles(prev => [...prev, newBubble]);

    // 自动隐藏
    setTimeout(() => {
      hideBubble(id);
    }, options.duration || 3000);

    return id;
  };

  const hideBubble = (id: string) => {
    setBubbles(prev => prev.map(bubble => 
      bubble.id === id ? { ...bubble, visible: false } : bubble
    ));

    // 延迟移除以允许退出动画
    setTimeout(() => {
      setBubbles(prev => prev.filter(bubble => bubble.id !== id));
    }, 300);
  };

  const hideAllBubbles = () => {
    setBubbles(prev => prev.map(bubble => ({ ...bubble, visible: false })));
    setTimeout(() => {
      setBubbles([]);
    }, 300);
  };

  const showThought = (text: string, speakerName?: string) => {
    return showBubble(text, { variant: 'thought', speakerName });
  };

  const showShout = (text: string, speakerName?: string) => {
    return showBubble(text, { variant: 'shout', speakerName });
  };

  const showWhisper = (text: string, speakerName?: string) => {
    return showBubble(text, { variant: 'whisper', speakerName });
  };

  return {
    bubbles,
    showBubble,
    hideBubble,
    hideAllBubbles,
    showThought,
    showShout,
    showWhisper
  };
} 