import React, { useEffect, useRef, useState } from 'react';
import { useLive2D } from '@/hooks/useLive2D';
import { cn } from '@/lib/utils';

interface Live2DContainerProps {
  className?: string;
  width?: number;
  height?: number;
  position?: 'fixed' | 'absolute' | 'relative';
  bottom?: number | string;
  right?: number | string;
  zIndex?: number;
  autoLoad?: boolean;
  enableInteraction?: boolean;
}

export function Live2DContainer({
  className,
  width = 400,
  height = 600,
  position = 'fixed',
  bottom = 0,
  right = 20,
  zIndex = 20,
  autoLoad = true,
  enableInteraction = true
}: Live2DContainerProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  
  // 使用Live2D Hook
  const live2d = useLive2D({
    autoLoadDefault: autoLoad,
    enableInteraction,
    enableMouseTracking: enableInteraction
  });

  // 初始化Live2D容器
  useEffect(() => {
    const initializeLive2D = async () => {
      if (!containerRef.current || isInitialized) return;

      try {
        await live2d.initialize(containerRef.current);
        setIsInitialized(true);
        console.log('Live2D容器初始化成功');
      } catch (error) {
        console.error('Live2D容器初始化失败:', error);
      }
    };

    initializeLive2D();
  }, [live2d, isInitialized]);

  // 清理资源
  useEffect(() => {
    return () => {
      if (isInitialized) {
        live2d.destroy();
      }
    };
  }, [live2d, isInitialized]);

  // 处理键盘快捷键
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (!live2d.state.currentModel || live2d.state.isLoading) return;

      // 防止在输入框中触发
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return;
      }

      switch (event.key) {
        case ' ': // 空格键 - 随机表情
          event.preventDefault();
          live2d.playRandomExpression();
          break;
        case 'Enter': // 回车键 - 随机动作
          event.preventDefault();
          live2d.playRandomMotion();
          break;
        case 'r': // R键 - 重置
        case 'R':
          event.preventDefault();
          live2d.resetModel();
          break;
        case 'e': // E键 - 切换表情
        case 'E':
          event.preventDefault();
          live2d.toggleExpression();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [live2d]);

  const containerStyle: React.CSSProperties = {
    position,
    bottom,
    right,
    zIndex,
    width,
    height,
    pointerEvents: enableInteraction ? 'auto' : 'none',
  };

  return (
    <div
      ref={containerRef}
      className={cn(
        "live2d-container",
        "transition-opacity duration-500",
        isInitialized ? "opacity-100" : "opacity-0",
        className
      )}
      style={containerStyle}
    >
      {/* 这里是Live2D模型渲染的地方 */}
      {/* PIXI Application 的 canvas 会被添加到这个容器中 */}
      
      {!isInitialized && (
        <div className="flex items-center justify-center h-full bg-black/20 rounded-lg border border-border/50">
          <div className="text-center text-muted-foreground">
            <div className="animate-spin w-8 h-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-2" />
            <div className="text-sm font-mono">Loading Live2D...</div>
          </div>
        </div>
      )}
    </div>
  );
}

// 导出Live2D Hook，方便其他组件使用
export { useLive2D } from '@/hooks/useLive2D'; 