import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { Activity, Zap, Clock, Database } from "lucide-react"

interface ProgressData {
  status: string
  progress: number
  chunkCount: number
  totalSize: number
  elapsedTime?: number
  estimatedTime?: number
}

interface ProgressPanelProps {
  data: ProgressData
  isActive?: boolean
  className?: string
}

export const ProgressPanel: React.FC<ProgressPanelProps> = ({
  data,
  isActive = false,
  className
}) => {
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'processing':
      case 'synthesizing':
        return 'info'
      case 'complete':
      case 'finished':
        return 'success'
      case 'error':
      case 'failed':
        return 'error'
      case 'waiting':
      case 'queued':
        return 'warning'
      default:
        return 'outline'
    }
  }

  return (
    <Card className={cn(
      "border-primary/20 bg-background/95 backdrop-blur transition-all duration-300",
      isActive && "processing-animation border-primary/40",
      className
    )}>
      <CardHeader className="pb-3">
        <CardTitle className="text-primary font-mono text-sm flex items-center gap-2">
          <Activity className={cn(
            "w-4 h-4",
            isActive && "animate-pulse"
          )} />
          SYNTHESIS PROGRESS
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 状态和进度 */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Badge 
              variant={getStatusColor(data.status)}
              className="font-mono text-xs"
            >
              {data.status.toUpperCase()}
            </Badge>
            <span className="text-sm font-mono text-primary">
              {Math.round(data.progress)}%
            </span>
          </div>
          
          <Progress 
            value={data.progress} 
            className={cn(
              "h-2 transition-all duration-300",
              isActive && "animate-pulse"
            )}
          />
        </div>

        {/* 详细信息网格 */}
        <div className="grid grid-cols-2 gap-4 text-xs font-mono">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1 text-muted-foreground">
                <Zap className="w-3 h-3" />
                <span>Chunks:</span>
              </div>
              <span className="text-primary">
                {data.chunkCount}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1 text-muted-foreground">
                <Database className="w-3 h-3" />
                <span>Size:</span>
              </div>
              <span className="text-primary">
                {formatBytes(data.totalSize)}
              </span>
            </div>
          </div>
          
          <div className="space-y-2">
            {data.elapsedTime !== undefined && (
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1 text-muted-foreground">
                  <Clock className="w-3 h-3" />
                  <span>Elapsed:</span>
                </div>
                <span className="text-primary">
                  {formatTime(data.elapsedTime)}
                </span>
              </div>
            )}
            
            {data.estimatedTime !== undefined && (
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1 text-muted-foreground">
                  <Clock className="w-3 h-3" />
                  <span>ETA:</span>
                </div>
                <span className="text-primary">
                  {formatTime(data.estimatedTime)}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* 进度条下方的状态指示器 */}
        {isActive && (
          <div className="flex items-center justify-center pt-2">
            <div className="flex space-x-1">
              {[0, 1, 2].map((i) => (
                <div
                  key={i}
                  className={cn(
                    "w-2 h-2 rounded-full bg-primary/30",
                    "animate-pulse"
                  )}
                  style={{
                    animationDelay: `${i * 0.2}s`,
                    animationDuration: '1s'
                  }}
                />
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default ProgressPanel