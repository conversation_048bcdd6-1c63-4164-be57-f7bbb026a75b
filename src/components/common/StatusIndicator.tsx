import React from 'react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { 
  Wifi, 
  WifiOff, 
  Loader2, 
  AlertTriangle, 
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react';

export type StatusType = 
  | 'online' 
  | 'offline' 
  | 'connecting' 
  | 'processing' 
  | 'error' 
  | 'warning' 
  | 'success'
  | 'idle';

interface StatusIndicatorProps {
  status: StatusType;
  message?: string;
  className?: string;
  showIcon?: boolean;
  showMessage?: boolean;
  variant?: 'default' | 'minimal' | 'detailed';
}

export function StatusIndicator({ 
  status, 
  message, 
  className,
  showIcon = true,
  showMessage = true,
  variant = 'default'
}: StatusIndicatorProps) {
  const getStatusConfig = () => {
    switch (status) {
      case 'online':
        return {
          icon: Wifi,
          badgeVariant: 'success' as const,
          label: 'ONLINE',
          iconClassName: 'text-green-400',
          pulseClass: ''
        };
      case 'connecting':
        return {
          icon: Loader2,
          badgeVariant: 'info' as const,
          label: 'CONNECTING',
          iconClassName: 'text-blue-400 animate-spin',
          pulseClass: 'animate-pulse'
        };
      case 'processing':
        return {
          icon: Loader2,
          badgeVariant: 'warning' as const,
          label: 'PROCESSING',
          iconClassName: 'text-orange-400 animate-spin',
          pulseClass: 'animate-pulse-glow'
        };
      case 'error':
        return {
          icon: XCircle,
          badgeVariant: 'error' as const,
          label: 'ERROR',
          iconClassName: 'text-red-400',
          pulseClass: 'animate-pulse'
        };
      case 'warning':
        return {
          icon: AlertTriangle,
          badgeVariant: 'warning' as const,
          label: 'WARNING',
          iconClassName: 'text-yellow-400',
          pulseClass: ''
        };
      case 'success':
        return {
          icon: CheckCircle,
          badgeVariant: 'success' as const,
          label: 'SUCCESS',
          iconClassName: 'text-green-400',
          pulseClass: ''
        };
      case 'idle':
        return {
          icon: Clock,
          badgeVariant: 'outline' as const,
          label: 'IDLE',
          iconClassName: 'text-muted-foreground',
          pulseClass: ''
        };
      default: // offline
        return {
          icon: WifiOff,
          badgeVariant: 'error' as const,
          label: 'OFFLINE',
          iconClassName: 'text-red-400',
          pulseClass: 'animate-pulse'
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;
  const displayMessage = message || config.label;

  if (variant === 'minimal') {
    return (
      <div className={cn(
        "flex items-center gap-2",
        className
      )}>
        {showIcon && (
          <Icon className={cn("w-4 h-4", config.iconClassName)} />
        )}
        {showMessage && (
          <span className="text-xs font-mono uppercase tracking-wider text-muted-foreground">
            {displayMessage}
          </span>
        )}
      </div>
    );
  }

  if (variant === 'detailed') {
    return (
      <div className={cn(
        "flex flex-col gap-1 p-3 rounded-lg border bg-background/50 backdrop-blur-sm",
        config.pulseClass,
        className
      )}>
        <div className="flex items-center gap-2">
          {showIcon && (
            <Icon className={cn("w-4 h-4", config.iconClassName)} />
          )}
          <Badge variant={config.badgeVariant} className="font-mono text-xs">
            {config.label}
          </Badge>
        </div>
        {showMessage && message && (
          <p className="text-xs text-muted-foreground font-mono">
            {message}
          </p>
        )}
      </div>
    );
  }

  // Default variant
  return (
    <Badge 
      variant={config.badgeVariant}
      className={cn(
        "font-mono uppercase tracking-wider px-3 py-1 text-xs",
        "flex items-center gap-2",
        config.pulseClass,
        className
      )}
    >
      {showIcon && (
        <Icon className={cn("w-3 h-3", config.iconClassName)} />
      )}
      {showMessage && displayMessage}
    </Badge>
  );
}