import React from 'react';
import { cn } from '@/lib/utils';
import { Loader2, Terminal, Cpu, Zap } from 'lucide-react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'terminal' | 'cyber' | 'pulse' | 'dots' | 'bars';
  className?: string;
  message?: string;
  showMessage?: boolean;
}

export function LoadingSpinner({
  size = 'md',
  variant = 'default',
  className,
  message,
  showMessage = false
}: LoadingSpinnerProps) {
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'w-4 h-4';
      case 'lg':
        return 'w-8 h-8';
      case 'xl':
        return 'w-12 h-12';
      default:
        return 'w-6 h-6';
    }
  };

  const getContainerSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'gap-2 text-xs';
      case 'lg':
        return 'gap-4 text-base';
      case 'xl':
        return 'gap-4 text-lg';
      default:
        return 'gap-3 text-sm';
    }
  };

  const renderSpinner = () => {
    const sizeClasses = getSizeClasses();

    switch (variant) {
      case 'terminal':
        return (
          <div className={cn("relative", sizeClasses)}>
            <Terminal className={cn(
              "text-green-400 animate-pulse",
              sizeClasses
            )} />
            <div className="absolute inset-0 border-2 border-green-400/30 rounded border-dashed animate-spin" />
          </div>
        );

      case 'cyber':
        return (
          <div className={cn("relative", sizeClasses)}>
            <div className={cn(
              "border-2 border-cyan-400/30 rounded-full animate-spin",
              "border-t-cyan-400 border-r-cyan-400",
              sizeClasses
            )} />
            <Cpu className={cn(
              "absolute inset-0 m-auto text-cyan-400 animate-pulse",
              size === 'sm' ? 'w-2 h-2' :
              size === 'lg' ? 'w-4 h-4' :
              size === 'xl' ? 'w-6 h-6' : 'w-3 h-3'
            )} />
          </div>
        );

      case 'pulse':
        return (
          <div className={cn("relative", sizeClasses)}>
            <div className={cn(
              "bg-primary rounded-full animate-pulse",
              sizeClasses
            )} />
            <div className={cn(
              "absolute inset-0 bg-primary rounded-full animate-ping opacity-30",
              sizeClasses
            )} />
          </div>
        );

      case 'dots':
        return (
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className={cn(
                  "bg-primary rounded-full",
                  size === 'sm' ? 'w-1 h-1' :
                  size === 'lg' ? 'w-3 h-3' :
                  size === 'xl' ? 'w-4 h-4' : 'w-2 h-2',
                  "animate-bounce"
                )}
                style={{
                  animationDelay: `${i * 0.2}s`
                }}
              />
            ))}
          </div>
        );

      case 'bars':
        return (
          <div className="flex space-x-1 items-end">
            {[0, 1, 2, 3].map((i) => (
              <div
                key={i}
                className={cn(
                  "bg-primary animate-pulse",
                  size === 'sm' ? 'w-0.5 h-3' :
                  size === 'lg' ? 'w-1 h-6' :
                  size === 'xl' ? 'w-1.5 h-8' : 'w-1 h-4'
                )}
                style={{
                  animationDelay: `${i * 0.1}s`,
                  animationDuration: '0.8s'
                }}
              />
            ))}
          </div>
        );

      default:
        return (
          <Loader2 className={cn(
            "animate-spin text-primary",
            sizeClasses
          )} />
        );
    }
  };

  if (showMessage || message) {
    return (
      <div className={cn(
        "flex flex-col items-center justify-center",
        getContainerSizeClasses(),
        className
      )}>
        {renderSpinner()}
        {(showMessage || message) && (
          <span className="font-mono text-muted-foreground uppercase tracking-wider">
            {message || 'Loading...'}
          </span>
        )}
      </div>
    );
  }

  return (
    <div className={cn("flex items-center justify-center", className)}>
      {renderSpinner()}
    </div>
  );
}

// 全屏加载组件
export function FullScreenLoader({
  message = "系统初始化中...",
  variant = 'cyber'
}: {
  message?: string;
  variant?: LoadingSpinnerProps['variant'];
}) {
  return (
    <div className="fixed inset-0 bg-background/95 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="text-center space-y-6">
        <LoadingSpinner
          size="xl"
          variant={variant}
          showMessage
          message={message}
        />
        
        {/* 科技感装饰 */}
        <div className="flex items-center justify-center space-x-4">
          <div className="w-8 h-px bg-gradient-to-r from-transparent to-primary" />
          <Zap className="w-4 h-4 text-primary animate-pulse" />
          <div className="w-8 h-px bg-gradient-to-l from-transparent to-primary" />
        </div>
      </div>
    </div>
  );
} 