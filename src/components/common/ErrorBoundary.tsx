import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, RefreshCw, Bug, Terminal } from 'lucide-react';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{
    error?: Error;
    resetError: () => void;
  }>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): <PERSON>rrorBoundaryState {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // 调用错误回调
    this.props.onError?.(error, errorInfo);

    // 记录错误到控制台
    console.error('ErrorBoundary caught an error:', error, errorInfo);

    // 在生产环境中，这里可以发送错误报告到监控服务
    if (process.env.NODE_ENV === 'production') {
      this.reportError(error, errorInfo);
    }
  }

  private reportError = (error: Error, errorInfo: React.ErrorInfo) => {
    // 错误上报逻辑
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    };

    // 这里可以发送到错误监控服务
    console.log('Error Report:', errorReport);
  };

  private resetError = () => {
    this.setState({
      hasError: false,
      error: undefined,
      errorInfo: undefined,
    });
  };

  private reloadPage = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback组件，使用它
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return (
          <FallbackComponent 
            error={this.state.error} 
            resetError={this.resetError} 
          />
        );
      }

      // 默认错误UI
      return (
        <div className="min-h-screen bg-background flex items-center justify-center p-4">
          <Card className="w-full max-w-2xl border-red-500/20 bg-background/95 backdrop-blur-sm">
            <CardHeader className="text-center">
              <div className="flex items-center justify-center mb-4">
                <div className="relative">
                  <Terminal className="w-12 h-12 text-red-400" />
                  <AlertTriangle className="w-6 h-6 text-red-500 absolute -top-1 -right-1" />
                </div>
              </div>
              <CardTitle className="text-2xl font-orbitron text-red-400">
                SYSTEM ERROR
              </CardTitle>
              <p className="text-muted-foreground font-mono mt-2">
                应用程序遇到了一个意外错误
              </p>
            </CardHeader>
            
            <CardContent className="space-y-6">
              {/* 错误信息 */}
              <div className="p-4 rounded-lg bg-red-500/10 border border-red-500/20">
                <div className="flex items-center gap-2 mb-2">
                  <Bug className="w-4 h-4 text-red-400" />
                  <Badge variant="error" className="font-mono text-xs">
                    ERROR
                  </Badge>
                </div>
                <p className="text-sm font-mono text-red-300 mb-2">
                  {this.state.error?.message || '未知错误'}
                </p>
                
                {/* 在开发环境中显示详细错误信息 */}
                {process.env.NODE_ENV === 'development' && this.state.error?.stack && (
                  <details className="mt-3">
                    <summary className="text-xs text-muted-foreground cursor-pointer hover:text-foreground">
                      显示详细错误信息
                    </summary>
                    <pre className="mt-2 text-xs text-muted-foreground bg-background/50 p-3 rounded border overflow-auto max-h-40">
                      {this.state.error.stack}
                    </pre>
                  </details>
                )}
              </div>

              {/* 操作按钮 */}
              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  onClick={this.resetError}
                  className="flex-1 font-mono"
                  variant="outline"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  重试
                </Button>
                
                <Button
                  onClick={this.reloadPage}
                  className="flex-1 font-mono"
                  variant="default"
                >
                  <Terminal className="w-4 h-4 mr-2" />
                  重新加载页面
                </Button>
              </div>

              {/* 帮助信息 */}
              <div className="text-xs text-muted-foreground font-mono text-center space-y-1">
                <p>如果问题持续存在，请尝试以下操作：</p>
                <ul className="list-disc list-inside space-y-1 text-left">
                  <li>清除浏览器缓存和Cookie</li>
                  <li>检查网络连接</li>
                  <li>使用最新版本的浏览器</li>
                  <li>联系技术支持</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export { ErrorBoundary };

// 简化的函数式错误边界Hook（仅用于开发环境）
export function useErrorHandler() {
  return (error: Error, errorInfo?: React.ErrorInfo) => {
    console.error('Handled error:', error, errorInfo);
    
    // 在实际应用中，这里可以发送错误到监控服务
    if (process.env.NODE_ENV === 'production') {
      // 发送错误报告
    }
  };
} 