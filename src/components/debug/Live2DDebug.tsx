import React, { useEffect, useState } from 'react';
import { checkLive2DEnvironment, getSupportedFormats } from '@/lib/live2d-setup';

interface DebugInfo {
  environment: ReturnType<typeof checkLive2DEnvironment>;
  formats: ReturnType<typeof getSupportedFormats>;
  pixiVersion: string;
  userAgent: string;
}

export function Live2DDebug() {
  const [debugInfo, setDebugInfo] = useState<DebugInfo | null>(null);

  useEffect(() => {
    const info: DebugInfo = {
      environment: checkLive2DEnvironment(),
      formats: getSupportedFormats(),
      pixiVersion: window.PIXI?.VERSION || 'Unknown',
      userAgent: navigator.userAgent
    };
    
    setDebugInfo(info);
    console.log('Live2D Debug Info:', info);
  }, []);

  if (!debugInfo) {
    return <div>Loading debug info...</div>;
  }

  return (
    <div className="p-4 bg-gray-100 rounded-lg font-mono text-sm">
      <h3 className="font-bold mb-4">Live2D Debug Information</h3>
      
      <div className="space-y-4">
        <div>
          <h4 className="font-semibold">Environment Status:</h4>
          <div className={`p-2 rounded ${debugInfo.environment.isReady ? 'bg-green-100' : 'bg-red-100'}`}>
            <div>Ready: {debugInfo.environment.isReady ? '✅' : '❌'}</div>
            <div>PIXI: {debugInfo.environment.checks.pixi ? '✅' : '❌'}</div>
            <div>Live2D: {debugInfo.environment.checks.live2d ? '✅' : '❌'}</div>
            <div>Ticker: {debugInfo.environment.checks.ticker ? '✅' : '❌'}</div>
          </div>
        </div>

        <div>
          <h4 className="font-semibold">Supported Formats:</h4>
          <div className="p-2 bg-blue-100 rounded">
            <div>Cubism 2: {debugInfo.formats.cubism2.join(', ')}</div>
            <div>Cubism 4: {debugInfo.formats.cubism4.join(', ')}</div>
          </div>
        </div>

        <div>
          <h4 className="font-semibold">System Info:</h4>
          <div className="p-2 bg-gray-200 rounded">
            <div>PIXI Version: {debugInfo.pixiVersion}</div>
            <div>User Agent: {debugInfo.userAgent.substring(0, 100)}...</div>
          </div>
        </div>
      </div>
    </div>
  );
}
