// Live2D 初始化和配置
import * as PIXI from 'pixi.js';
import { Live2DModel } from 'pixi-live2d-display';

// 确保 PIXI 全局可用
declare global {
  interface Window {
    PIXI: typeof PIXI;
  }
}

// 设置全局 PIXI
window.PIXI = PIXI;
if (typeof globalThis !== 'undefined') {
  globalThis.PIXI = PIXI;
}

/**
 * 初始化 Live2D 环境
 */
export function initializeLive2D() {
  try {
    console.log('开始初始化 Live2D 环境...');

    // 确保 PIXI 全局可用
    if (!window.PIXI) {
      window.PIXI = PIXI;
      console.log('已设置 window.PIXI');
    }

    // 注册必要的 PIXI 插件
    if (PIXI.Application && PIXI.Ticker) {
      // 注册 Ticker 插件用于自动更新
      Live2DModel.registerTicker(PIXI.Ticker);
      console.log('已注册 PIXI Ticker');
    }

    // 检查 Live2D 模型类是否可用
    if (Live2DModel) {
      console.log('Live2DModel 类可用');
    } else {
      console.warn('Live2DModel 类不可用');
    }

    console.log('Live2D 环境初始化成功');
    return true;
  } catch (error) {
    console.error('Live2D 环境初始化失败:', error);
    return false;
  }
}

/**
 * 检查 Live2D 环境是否就绪
 */
export function checkLive2DEnvironment() {
  const checks = {
    pixi: !!window.PIXI,
    live2d: !!Live2DModel,
    ticker: !!(window.PIXI && window.PIXI.Ticker),
  };

  const isReady = Object.values(checks).every(Boolean);

  if (!isReady) {
    console.warn('Live2D 环境检查失败:', checks);
  }

  return {
    isReady,
    checks
  };
}

/**
 * 获取 Live2D 支持的模型格式
 */
export function getSupportedFormats() {
  return {
    cubism2: ['.model.json'],
    cubism4: ['.model3.json'],
    supported: ['.model.json', '.model3.json']
  };
}

/**
 * 检测模型格式
 */
export function detectModelFormat(modelPath: string): 'cubism2' | 'cubism4' | 'unknown' {
  if (modelPath.endsWith('.model.json')) {
    return 'cubism2';
  } else if (modelPath.endsWith('.model3.json')) {
    return 'cubism4';
  }
  return 'unknown';
}

// 自动初始化
initializeLive2D();
