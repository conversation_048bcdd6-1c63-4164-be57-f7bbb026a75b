#!/usr/bin/env node

/**
 * Live2D模型集成测试脚本
 * 用于测试模型配置和文件完整性
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const access = promisify(fs.access);
const readFile = promisify(fs.readFile);

/**
 * 测试模型配置文件
 */
async function testModelConfig() {
  console.log('测试模型配置文件...');
  
  const configPath = path.join(__dirname, '..', 'public', 'static', 'live2d', 'config', 'models.json');
  
  try {
    await access(configPath);
    const configData = await readFile(configPath, 'utf8');
    const config = JSON.parse(configData);
    
    console.log(`✓ 配置文件加载成功`);
    console.log(`✓ 版本: ${config.version}`);
    console.log(`✓ 默认模型: ${config.defaultModel}`);
    console.log(`✓ 模型数量: ${config.models.length}`);
    
    // 验证每个模型配置
    for (const model of config.models) {
      console.log(`\n模型: ${model.name} (${model.id})`);
      console.log(`  路径: ${model.modelPath}`);
      console.log(`  表情数量: ${model.expressions.length}`);
      console.log(`  动作数量: ${model.motions.length}`);
      
      // 检查必需字段
      const requiredFields = ['id', 'name', 'modelPath'];
      for (const field of requiredFields) {
        if (!model[field]) {
          console.log(`  ✗ 缺少必需字段: ${field}`);
        } else {
          console.log(`  ✓ ${field}: ${model[field]}`);
        }
      }
    }
    
    return true;
  } catch (error) {
    console.error('✗ 配置文件测试失败:', error.message);
    return false;
  }
}

/**
 * 测试模型文件存在性
 */
async function testModelFiles() {
  console.log('\n测试模型文件存在性...');
  
  const configPath = path.join(__dirname, '..', 'public', 'static', 'live2d', 'config', 'models.json');
  
  try {
    const configData = await readFile(configPath, 'utf8');
    const config = JSON.parse(configData);
    
    for (const model of config.models) {
      console.log(`\n检查模型: ${model.name}`);
      
      // 检查主模型文件
      const modelPath = path.join(__dirname, '..', 'public', model.modelPath);
      try {
        await access(modelPath);
        console.log(`  ✓ 主文件存在: ${model.modelPath}`);
        
        // 解析模型文件
        const modelData = JSON.parse(await readFile(modelPath, 'utf8'));
        console.log(`  ✓ 模型版本: ${modelData.Version}`);
        
        // 检查相关文件
        const basePath = path.dirname(modelPath);
        const fileReferences = modelData.FileReferences;
        
        if (fileReferences.Moc) {
          const mocPath = path.join(basePath, fileReferences.Moc);
          try {
            await access(mocPath);
            console.log(`  ✓ MOC文件存在: ${fileReferences.Moc}`);
          } catch (error) {
            console.log(`  ✗ MOC文件缺失: ${fileReferences.Moc}`);
          }
        }
        
        if (fileReferences.Textures) {
          for (const texture of fileReferences.Textures) {
            const texturePath = path.join(basePath, texture);
            try {
              await access(texturePath);
              console.log(`  ✓ 纹理文件存在: ${texture}`);
            } catch (error) {
              console.log(`  ✗ 纹理文件缺失: ${texture}`);
            }
          }
        }
        
      } catch (error) {
        console.log(`  ✗ 主文件缺失: ${model.modelPath}`);
      }
    }
    
    return true;
  } catch (error) {
    console.error('✗ 模型文件测试失败:', error.message);
    return false;
  }
}

/**
 * 测试TypeScript类型定义
 */
async function testTypeDefinitions() {
  console.log('\n测试TypeScript类型定义...');
  
  const typePath = path.join(__dirname, '..', 'src', 'types', 'live2d.ts');
  
  try {
    await access(typePath);
    const typeContent = await readFile(typePath, 'utf8');
    
    // 检查关键接口
    const requiredInterfaces = [
      'Live2DModel',
      'Live2DConfig',
      'Live2DModelState',
      'Live2DExpression',
      'Live2DMotion'
    ];
    
    for (const interfaceName of requiredInterfaces) {
      if (typeContent.includes(`interface ${interfaceName}`)) {
        console.log(`  ✓ 接口定义存在: ${interfaceName}`);
      } else {
        console.log(`  ✗ 接口定义缺失: ${interfaceName}`);
      }
    }
    
    return true;
  } catch (error) {
    console.error('✗ 类型定义测试失败:', error.message);
    return false;
  }
}

/**
 * 测试服务类文件
 */
async function testServiceFiles() {
  console.log('\n测试服务类文件...');
  
  const serviceFiles = [
    'src/services/live2d/Live2DService.ts',
    'src/services/live2d/ModelConfigAdapter.ts',
    'src/services/live2d/ResourceManager.ts',
    'src/hooks/useLive2D.ts'
  ];
  
  for (const serviceFile of serviceFiles) {
    const filePath = path.join(__dirname, '..', serviceFile);
    try {
      await access(filePath);
      console.log(`  ✓ 服务文件存在: ${serviceFile}`);
    } catch (error) {
      console.log(`  ✗ 服务文件缺失: ${serviceFile}`);
    }
  }
  
  return true;
}

/**
 * 生成测试报告
 */
function generateReport(results) {
  console.log('\n' + '='.repeat(50));
  console.log('Live2D集成测试报告');
  console.log('='.repeat(50));
  
  const testNames = [
    '模型配置文件',
    '模型文件存在性',
    'TypeScript类型定义',
    '服务类文件'
  ];
  
  let passedCount = 0;
  
  results.forEach((result, index) => {
    const status = result ? '✓ 通过' : '✗ 失败';
    console.log(`${testNames[index]}: ${status}`);
    if (result) passedCount++;
  });
  
  console.log('-'.repeat(30));
  console.log(`总计: ${passedCount}/${results.length} 项测试通过`);
  
  if (passedCount === results.length) {
    console.log('🎉 所有测试通过！Live2D模型集成完成。');
  } else {
    console.log('⚠️  部分测试失败，请检查相关配置。');
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('开始Live2D模型集成测试...\n');
  
  const results = [];
  
  results.push(await testModelConfig());
  results.push(await testModelFiles());
  results.push(await testTypeDefinitions());
  results.push(await testServiceFiles());
  
  generateReport(results);
  
  return results.every(result => result);
}

// 运行测试
if (require.main === module) {
  runTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('测试执行失败:', error);
      process.exit(1);
    });
}

module.exports = {
  runTests,
  testModelConfig,
  testModelFiles,
  testTypeDefinitions,
  testServiceFiles
};