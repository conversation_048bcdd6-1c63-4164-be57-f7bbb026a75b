#!/usr/bin/env node

/**
 * Live2D官方模型下载脚本
 * 用于下载和管理Live2D官方示例模型
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const { promisify } = require('util');

const mkdir = promisify(fs.mkdir);
const writeFile = promisify(fs.writeFile);
const access = promisify(fs.access);

// 官方模型CDN配置
const OFFICIAL_MODELS = {
  haru: {
    name: 'Haru',
    baseUrl: 'https://cdn.jsdelivr.net/gh/guansss/pixi-live2d-display/test/assets/haru/',
    files: [
      'haru_greeter_t03.model3.json',
      'haru_greeter_t03.moc3',
      'haru_greeter_t03.physics3.json',
      'haru_greeter_t03.cdi3.json',
      'textures/texture_00.png',
      'motions/idle_01.motion3.json',
      'motions/tap_01.motion3.json',
      'motions/flick_head_01.motion3.json',
      'motions/shake_01.motion3.json',
      'expressions/f01.exp3.json',
      'expressions/f02.exp3.json',
      'expressions/f03.exp3.json',
      'expressions/f04.exp3.json',
      'expressions/f05.exp3.json',
      'expressions/f06.exp3.json',
      'expressions/f07.exp3.json',
      'expressions/f08.exp3.json'
    ]
  },
  mark: {
    name: 'Mark',
    baseUrl: 'https://cdn.jsdelivr.net/gh/guansss/pixi-live2d-display/test/assets/mark/',
    files: [
      'mark_free_t03.model3.json',
      'mark_free_t03.moc3',
      'mark_free_t03.physics3.json',
      'mark_free_t03.cdi3.json',
      'textures/texture_00.png',
      'motions/idle_01.motion3.json',
      'motions/tap_01.motion3.json',
      'motions/flick_head_01.motion3.json',
      'motions/shake_01.motion3.json',
      'expressions/f01.exp3.json',
      'expressions/f02.exp3.json',
      'expressions/f03.exp3.json',
      'expressions/f04.exp3.json',
      'expressions/f05.exp3.json',
      'expressions/f06.exp3.json',
      'expressions/f07.exp3.json'
    ]
  },
  natori: {
    name: 'Natori',
    baseUrl: 'https://cdn.jsdelivr.net/gh/guansss/pixi-live2d-display/test/assets/natori/',
    files: [
      'natori_free_t02.model3.json',
      'natori_free_t02.moc3',
      'natori_free_t02.physics3.json',
      'natori_free_t02.cdi3.json',
      'textures/texture_00.png',
      'motions/idle_01.motion3.json',
      'motions/tap_01.motion3.json',
      'motions/flick_head_01.motion3.json',
      'motions/shake_01.motion3.json',
      'expressions/f01.exp3.json',
      'expressions/f02.exp3.json',
      'expressions/f03.exp3.json',
      'expressions/f04.exp3.json',
      'expressions/f05.exp3.json',
      'expressions/f06.exp3.json'
    ]
  }
};

/**
 * 下载文件
 */
function downloadFile(url, filePath) {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(filePath);
    
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`下载失败: ${response.statusCode} ${url}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        resolve();
      });
      
      file.on('error', (err) => {
        fs.unlink(filePath, () => {}); // 删除部分下载的文件
        reject(err);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

/**
 * 确保目录存在
 */
async function ensureDir(dirPath) {
  try {
    await access(dirPath);
  } catch (error) {
    await mkdir(dirPath, { recursive: true });
  }
}

/**
 * 下载单个模型
 */
async function downloadModel(modelId) {
  const model = OFFICIAL_MODELS[modelId];
  if (!model) {
    throw new Error(`未知的模型: ${modelId}`);
  }

  console.log(`开始下载模型: ${model.name}`);
  
  const modelDir = path.join(__dirname, '..', 'public', 'static', 'live2d', 'models', modelId);
  await ensureDir(modelDir);

  let successCount = 0;
  let failCount = 0;

  for (const file of model.files) {
    const url = `${model.baseUrl}${file}`;
    const filePath = path.join(modelDir, file);
    
    // 确保文件目录存在
    await ensureDir(path.dirname(filePath));
    
    try {
      console.log(`  下载: ${file}`);
      await downloadFile(url, filePath);
      successCount++;
    } catch (error) {
      console.error(`  下载失败: ${file} - ${error.message}`);
      failCount++;
    }
  }

  console.log(`模型 ${model.name} 下载完成: 成功 ${successCount}, 失败 ${failCount}`);
  return { successCount, failCount };
}

/**
 * 下载所有模型
 */
async function downloadAllModels() {
  console.log('开始下载所有Live2D官方模型...');
  
  let totalSuccess = 0;
  let totalFail = 0;

  for (const modelId of Object.keys(OFFICIAL_MODELS)) {
    try {
      const result = await downloadModel(modelId);
      totalSuccess += result.successCount;
      totalFail += result.failCount;
    } catch (error) {
      console.error(`模型 ${modelId} 下载失败:`, error.message);
    }
  }

  console.log(`\n所有模型下载完成:`);
  console.log(`  总成功: ${totalSuccess}`);
  console.log(`  总失败: ${totalFail}`);
}

/**
 * 验证模型文件
 */
async function validateModel(modelId) {
  const model = OFFICIAL_MODELS[modelId];
  if (!model) {
    throw new Error(`未知的模型: ${modelId}`);
  }

  console.log(`验证模型: ${model.name}`);
  
  const modelDir = path.join(__dirname, '..', 'public', 'static', 'live2d', 'models', modelId);
  
  let existCount = 0;
  let missingCount = 0;

  for (const file of model.files) {
    const filePath = path.join(modelDir, file);
    
    try {
      await access(filePath);
      existCount++;
    } catch (error) {
      console.log(`  缺失: ${file}`);
      missingCount++;
    }
  }

  console.log(`模型 ${model.name} 验证结果: 存在 ${existCount}, 缺失 ${missingCount}`);
  return { existCount, missingCount };
}

/**
 * 验证所有模型
 */
async function validateAllModels() {
  console.log('验证所有Live2D模型...');
  
  for (const modelId of Object.keys(OFFICIAL_MODELS)) {
    try {
      await validateModel(modelId);
    } catch (error) {
      console.error(`模型 ${modelId} 验证失败:`, error.message);
    }
  }
}

/**
 * 生成模型信息
 */
function generateModelInfo() {
  console.log('Live2D官方模型信息:');
  console.log('='.repeat(50));
  
  for (const [modelId, model] of Object.entries(OFFICIAL_MODELS)) {
    console.log(`模型ID: ${modelId}`);
    console.log(`名称: ${model.name}`);
    console.log(`文件数量: ${model.files.length}`);
    console.log(`CDN地址: ${model.baseUrl}`);
    console.log('-'.repeat(30));
  }
}

// 命令行处理
const command = process.argv[2];
const modelId = process.argv[3];

async function main() {
  try {
    switch (command) {
      case 'download':
        if (modelId) {
          await downloadModel(modelId);
        } else {
          await downloadAllModels();
        }
        break;
        
      case 'validate':
        if (modelId) {
          await validateModel(modelId);
        } else {
          await validateAllModels();
        }
        break;
        
      case 'info':
        generateModelInfo();
        break;
        
      default:
        console.log('用法:');
        console.log('  node download-models.cjs download [modelId]  # 下载模型');
        console.log('  node download-models.cjs validate [modelId]  # 验证模型');
        console.log('  node download-models.cjs info                # 显示模型信息');
        console.log('');
        console.log('可用的模型ID:', Object.keys(OFFICIAL_MODELS).join(', '));
        break;
    }
  } catch (error) {
    console.error('执行失败:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  downloadModel,
  downloadAllModels,
  validateModel,
  validateAllModels,
  OFFICIAL_MODELS
};