{"Version": 3, "Meta": {"Duration": 2.983, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": false, "CurveCount": 3, "TotalSegmentCount": 37, "TotalPointCount": 106, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 1, 0.039, 0, 0.078, 30, 0.117, 30, 1, 1.022, 30, 1.928, 30, 2.833, 30, 1, 2.883, 30, 2.933, 0, 2.983, 0]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 27, 0, 0.117, 27, 1, 0.183, 27, 0.25, 11.219, 0.317, -7, 1, 0.383, -25.219, 0.45, -30, 0.517, -30, 1, 0.583, -30, 0.65, -15.477, 0.717, 3, 1, 0.783, 21.477, 0.85, 27, 0.917, 27, 1, 0.983, 27, 1.05, 11.219, 1.117, -7, 1, 1.183, -25.219, 1.25, -30, 1.317, -30, 1, 1.361, -30, 1.406, -17.083, 1.45, 3, 1, 1.489, 20.572, 1.528, 27, 1.567, 27, 1, 1.617, 27, 1.667, 7.435, 1.717, -7, 1, 1.783, -26.247, 1.85, -30, 1.917, -30, 1, 1.983, -30, 2.05, -18.709, 2.117, 3, 1, 2.167, 19.282, 2.217, 27, 2.267, 27, 1, 2.333, 27, 2.4, 11.219, 2.467, -7, 1, 2.533, -25.219, 2.6, -30, 2.667, -30, 1, 2.733, -30, 2.8, 3, 2.867, 3, 0, 2.983, 3]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, 9, 0, 0.117, 9, 1, 0.183, 9, 0.25, 30, 0.317, 30, 1, 0.383, 30, 0.45, 18.976, 0.517, -1, 1, 0.583, -20.976, 0.65, -30, 0.717, -30, 1, 0.783, -30, 0.85, -9.024, 0.917, 9, 1, 0.983, 27.024, 1.05, 30, 1.117, 30, 1, 1.183, 30, 1.25, 22.297, 1.317, -1, 1, 1.361, -16.532, 1.406, -30, 1.45, -30, 1, 1.489, -30, 1.528, -5.418, 1.567, 9, 1, 1.617, 27.537, 1.667, 30, 1.717, 30, 1, 1.783, 30, 1.85, 18.976, 1.917, -1, 1, 1.983, -20.976, 2.05, -30, 2.117, -30, 1, 2.167, -30, 2.217, -4.872, 2.267, 9, 1, 2.333, 27.496, 2.4, 30, 2.467, 30, 1, 2.533, 30, 2.6, 18.976, 2.667, -1, 1, 2.733, -20.976, 2.8, -30, 2.867, -30, 0, 2.983, -30]}]}