# Evercall TTS React

基于React技术栈的现代化文本转语音Web应用，集成Live2D虚拟角色、实时语音合成、粒子特效等功能。

## 技术栈

- **React 19** - 主框架
- **TypeScript** - 类型安全
- **Vite** - 构建工具
- **Tailwind CSS** - 样式框架
- **Radix UI** - 无样式UI组件库
- **Lucide React** - 图标库
- **pnpm** - 包管理器

## 项目结构

```
src/
├── components/          # React组件
│   ├── ui/             # 基础UI组件
│   ├── layout/         # 布局组件
│   ├── features/       # 功能组件
│   ├── common/         # 通用组件
│   └── providers/      # Context提供者
├── services/           # 服务层
├── store/              # 状态管理
├── hooks/              # 自定义Hooks
├── utils/              # 工具函数
├── lib/                # 库配置
├── types/              # TypeScript类型
└── styles/             # 样式文件
```

## 开发命令

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm run dev

# 构建生产版本
pnpm run build

# 代码检查
pnpm run lint

# 代码格式化
pnpm run format

# 类型检查
pnpm run type-check
```

## 环境变量

复制 `.env.development` 文件并根据需要修改配置：

```bash
VITE_APP_TITLE=Evercall TTS React
VITE_WEBSOCKET_URL=ws://localhost:8080
VITE_API_BASE_URL=http://localhost:8080
VITE_LIVE2D_CDN_URL=https://cdn.jsdelivr.net/gh/guansss/pixi-live2d-display/test/assets
```

## 功能特性

- 🎯 现代化React架构
- 🎨 Tailwind CSS样式系统
- 📱 响应式设计
- 🔧 TypeScript类型安全
- ⚡ Vite快速构建
- 🎭 Live2D虚拟角色集成
- 🔊 实时语音合成
- ✨ 粒子特效系统
- 📝 系统日志管理
- 💾 音频缓存功能

## 开发规范

- 使用TypeScript进行类型安全开发
- 遵循ESLint和Prettier代码规范
- 组件按功能模块组织
- 使用自定义Hooks封装逻辑
- 统一的错误处理和日志记录

## 许可证

本项目仅用于学习和非商业用途。