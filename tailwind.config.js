/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ["class"],
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        // 科技感配色
        cyber: {
          blue: "#00a2ff",
          green: "#00ff88",
          orange: "#ff6600",
          purple: "#8b5cf6",
          red: "#ff4757",
          gold: "#ffc107",
        },
        terminal: {
          green: "#00ff88",
          blue: "#00a2ff",
          amber: "#fbbf24",
          red: "#ef4444",
        }
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      fontFamily: {
        sans: ["Inter", "ui-sans-serif", "system-ui"],
        mono: ["JetBrains Mono", "Fira Code", "Consolas", "monospace"],
        orbitron: ["Orbitron", "monospace"],
        rajdhani: ["Rajdhani", "sans-serif"],
      },
      fontSize: {
        "2xs": ["0.625rem", { lineHeight: "0.75rem" }],
      },
      spacing: {
        18: "4.5rem",
        88: "22rem",
      },
      backdropBlur: {
        xs: "2px",
      },
      animation: {
        "fade-in": "fadeIn 0.5s ease-in-out",
        "fade-out": "fadeOut 0.5s ease-in-out",
        "slide-up": "slideUp 0.3s ease-out",
        "slide-down": "slideDown 0.3s ease-out",
        "slide-in-left": "slideInLeft 0.3s ease-out",
        "slide-in-right": "slideInRight 0.3s ease-out",
        "pulse-glow": "pulseGlow 2s ease-in-out infinite",
        "pulse-border": "pulseBorder 2s ease-in-out infinite",
        "bounce-soft": "bounceSoft 1s ease-in-out infinite",
        "glow": "glow 2s ease-in-out infinite alternate",
        "scan-line": "scanLine 2s linear infinite",
        "matrix-rain": "matrixRain 20s linear infinite",
        "terminal-cursor": "terminalCursor 1s step-end infinite",
        "cyber-pulse": "cyberPulse 3s ease-in-out infinite",
        "data-flow": "dataFlow 4s linear infinite",
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
      keyframes: {
        fadeIn: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        fadeOut: {
          "0%": { opacity: "1" },
          "100%": { opacity: "0" },
        },
        slideUp: {
          "0%": { transform: "translateY(10px)", opacity: "0" },
          "100%": { transform: "translateY(0)", opacity: "1" },
        },
        slideDown: {
          "0%": { transform: "translateY(-10px)", opacity: "0" },
          "100%": { transform: "translateY(0)", opacity: "1" },
        },
        slideInLeft: {
          "0%": { transform: "translateX(-10px)", opacity: "0" },
          "100%": { transform: "translateX(0)", opacity: "1" },
        },
        slideInRight: {
          "0%": { transform: "translateX(10px)", opacity: "0" },
          "100%": { transform: "translateX(0)", opacity: "1" },
        },
        pulseGlow: {
          "0%, 100%": { boxShadow: "0 0 5px rgba(0, 255, 136, 0.5)" },
          "50%": { boxShadow: "0 0 25px rgba(0, 255, 136, 0.8), 0 0 50px rgba(0, 255, 136, 0.3)" },
        },
        pulseBorder: {
          "0%, 100%": { borderColor: "rgba(0, 255, 136, 0.3)" },
          "50%": { borderColor: "rgba(0, 255, 136, 1)" },
        },
        bounceSoft: {
          "0%, 100%": { transform: "translateY(0)" },
          "50%": { transform: "translateY(-5px)" },
        },
        glow: {
          "0%": { 
            boxShadow: "0 0 5px rgba(0, 162, 255, 0.5), inset 0 0 5px rgba(0, 162, 255, 0.1)" 
          },
          "100%": { 
            boxShadow: "0 0 20px rgba(0, 162, 255, 0.8), inset 0 0 10px rgba(0, 162, 255, 0.2)" 
          },
        },
        scanLine: {
          "0%": { transform: "translateX(-100%)" },
          "100%": { transform: "translateX(100%)" },
        },
        matrixRain: {
          "0%": { transform: "translateY(-100%)" },
          "100%": { transform: "translateY(100vh)" },
        },
        terminalCursor: {
          "0%, 50%": { opacity: "1" },
          "51%, 100%": { opacity: "0" },
        },
        cyberPulse: {
          "0%, 100%": { 
            opacity: "0.4",
            transform: "scale(1)",
          },
          "50%": { 
            opacity: "1",
            transform: "scale(1.05)",
          },
        },
        dataFlow: {
          "0%": { 
            transform: "translateX(-100%) skewX(-15deg)",
            opacity: "0",
          },
          "50%": { 
            opacity: "1",
          },
          "100%": { 
            transform: "translateX(100%) skewX(-15deg)",
            opacity: "0",
          },
        },
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      backgroundImage: {
        'grid-pattern': `
          linear-gradient(rgba(0, 255, 136, 0.1) 1px, transparent 1px),
          linear-gradient(90deg, rgba(0, 255, 136, 0.1) 1px, transparent 1px)
        `,
        'cyber-grid': `
          linear-gradient(rgba(0, 162, 255, 0.15) 1px, transparent 1px),
          linear-gradient(90deg, rgba(0, 162, 255, 0.15) 1px, transparent 1px)
        `,
        'terminal-scan': `
          linear-gradient(90deg, transparent, rgba(0, 255, 136, 0.1), transparent)
        `,
      },
      boxShadow: {
        'terminal': '0 0 20px rgba(0, 255, 136, 0.3)',
        'cyber': '0 0 20px rgba(0, 162, 255, 0.4)',
        'neon-green': '0 0 5px #00ff88, 0 0 10px #00ff88, 0 0 15px #00ff88',
        'neon-blue': '0 0 5px #00a2ff, 0 0 10px #00a2ff, 0 0 15px #00a2ff',
        'inner-glow': 'inset 0 0 10px rgba(0, 255, 136, 0.2)',
      },
      gradientColorStops: {
        'cyber-start': '#00a2ff',
        'cyber-middle': '#8b5cf6',
        'cyber-end': '#00ff88',
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}